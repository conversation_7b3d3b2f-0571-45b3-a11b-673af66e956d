Jan 25 00:00:00 ar_logcat: Run Ar_logcat service.
Jan 25 00:00:00 ar_logcat: 5hJk2Ld8Rt9sFpQw^M
Jan 25 00:00:00 ar_logcat: vin start @ sdk version [5hJk2Ld8Rt9sFpQw] 
Jan 25 00:00:00 ar_logcat: ^[[35;22m[124390387][0300][0x7f81f5f200][SYS_CORE][VI_KEY] server.c: start_camera_server : 3693 load vin driver start^[[0m
Jan 25 00:00:01 ar_logcat: ^[[31;22m[124390390][0300][0x7f81f5f200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [/usrdata/local/factory/tunning/dpvif/dp_scaler_lut.bin] failed!^[[0m
Jan 25 00:00:01 ar_logcat: open the driver sns:dp_rx obj:stSnsdp_rxObj lib:libsns_dp_rx.so 
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390392][0300][0x7f81f5f200][SYS_CORE][VI_KEY] server.c: start_camera_server : 3957 load vin driver end max_sensor_count=5^[[0m
Jan 25 00:00:01 ar_logcat: Create socked success
Jan 25 00:00:01 ar_logcat: Bind socket success
Jan 25 00:00:01 ar_logcat: Listen socket success
Jan 25 00:00:01 ar_logcat: client accept thread running
Jan 25 00:00:01 ar_logcat: ^[[31;22m[25 00:00:00.955031][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3559 !!!!!!the vif is force to dpcm duce to reduce bandwidth, if you want to use the dng, please set the pstPipeAttr->enEnabl
Jan 25 00:00:01 ar_logcat: ^[[31;22m[25 00:00:00.955247][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3603 !!!!!!! Note: your app cfg the vif write compress on,so you must cfg the vif fre >=3*mipi fre !!!!!!^[[0m
Jan 25 00:00:01 ar_logcat: ^[[31;22m[25 00:00:00.956173][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7846 not supported addr type^[[0m
Jan 25 00:00:01 ar_logcat: ^[[31;22m[25 00:00:00.956211][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7856 not supported data type^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390393][0300][0x7f82382200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_open_camera : 920 camera_id=1^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390393][0300][0x7f812a9200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390393][0300][0x7f812a9200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390394][0300][0x7f813ae200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390394][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390394][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390394][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:01 ar_logcat: w h change from 0 0  to 1920 1080 fetch scaler lut 
Jan 25 00:00:01 ar_logcat: ^[[31;22m[124390394][0300][0x7f813ae200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [/usrdata/local/factory/tunning/dpvif/dp_scaler_lut.bin] failed!^[[0m
Jan 25 00:00:01 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:00:01 ar_logcat: 36823000 36a41000 36aca000 0 0 0 36b4d000 36d6b000 36df4000 0 0 0 
Jan 25 00:00:01 ar_logcat: aec index 1096686769 line 1125 gain 1.000000 
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390394][0300][0x7f813ae200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390394][0300][0x7f813ae200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390394][0300][0x7f813ae200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390395][0300][0x7f813ae200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:00:01 ar_logcat: ^[[33;22m[124390450][0300][0x7f813ae200][AIO_CORE][WARN] i2s.c: ar_i2s_io_dev_dma_start : 1345 Ao s32ChnEnCnt = 1 u32ChnCnt 2.
Jan 25 00:00:01 ar_logcat: ^[[0m
Jan 25 00:00:01 ar_logcat: ^[[33;22m[124390452][0300][0x7f812a9200][AIO_CORE][WARN] i2s.c: ar_i2s_io_dev_dma_start : 1345 Ai s32ChnEnCnt = 1 u32ChnCnt 2.
Jan 25 00:00:01 ar_logcat: ^[[0m
Jan 25 00:00:01 ar_logcat: ^[[33;22m[124390457][0300][0x7f81267200][VO_CORE][WARN] display_api.c: dc_init : 2258 use dc_reg_irq_by_cmd!^[[0m
Jan 25 00:00:01 ar_logcat: ^[[31;22m[124390457][0300][0x7f81267200][VO_CORE][ERROR] display_top.c: ar_display_dev_enable : 3521 not support set irq_type:if/dp_scaler_lut.bin] failed!^[[0m
Jan 25 00:00:01 ar_logcat: 0x7f80a45200][AXIMGR_CORE][WARN] ar_aximgr_lb.c: ar_aximgr_lb_enable : 285 [LB] 1920 1024 1024^[[0m
Jan 25 00:00:01 ar_logcat: ^[[33;22m[124390458][0300][0x7f813ae200][AXIMGR_CORE][WARN] ar_aximgr_lb.c: ar_aximgr_lb_enable : 285 [LB] 1920 1024 1024^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390458][0300][0x7f80a45200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390458][0300][0x7f80a45200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390458][0300][0x7f813ae200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390458][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390458][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:00:01 ar_logcat: w h change from 0 0  to 1920 1080 fetch scaler lut 
Jan 25 00:00:01 ar_logcat: ^[[31;22m[124390458][0300][0x7f813ae200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:01 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:00:01 ar_logcat: 36e7d000 3709b000 37124000 0 0 0 371a7000 373c5000 3744e000 0 0 0 
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390458][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:01 ar_logcat: aec index -168480335 line 1125 gain 1.000000 
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390458][0300][0x7f813ae200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390458][0300][0x7f813ae200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:01 ar_logcat: w h change from 1920 1080  to 1920 1080 fetch scaler lut 
Jan 25 00:00:01 ar_logcat: ^[[31;22m[124390459][0300][0x7f81616200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:01 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:00:01 ar_logcat: 374d7000 376f5000 3777e000 0 0 0 37801000 37a1f000 37aa8000 0 0 0 
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390460][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390461][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:00:02 ar_logcat: ^[[33;22m[124390522][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 3 read num 3
Jan 25 00:00:02 ar_logcat: ^[[0m
Jan 25 00:00:02 ar_logcat: ^[[33;22m[124390522][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 471 [ahb-3] i2s out int lost 2 free num 0
Jan 25 00:00:02 ar_logcat: ^[[0m
Jan 25 00:00:03 ar_logcat: open the driver sns:imx681 obj:stSnsimx681Obj lib:libsns_imx681.so 
Jan 25 00:00:03 ar_logcat: ^[[31;22m[25 00:00:03.277431][0313][0x7f657fa1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3554 !!!!!!!!!! Note:your app cfg the isp read and vif write  as no compress,this mode isp read and vif write will cost more 
Jan 25 00:00:03 ar_logcat: ^[[31;22m[25 00:00:03.277547][0313][0x7f657fa1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3657 stream id 0, no need cfg the sns_var_info^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390625][0300][0x7f81f5f200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_open_camera : 920 camera_id=0^[[0m
Jan 25 00:00:03 ar_logcat: ^[[33;22m[124390630][0300][0x7f813ae200][VO_CORE][WARN] display_api.c: dc_deinit : 2323 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:00:03 ar_logcat: ^[[33;22m[124390630][0300][0x7f8085d200][VO_CORE][WARN] display_api.c: dc_deinit : 2323 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390630][0300][0x7f813ae200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390630][0300][0x7f813ae200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390631][0300][0x7f80a45200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_close_camera : 961 camera_id=1^[[0m
Jan 25 00:00:03 ar_logcat: open the driver sns:dp_rx obj:stSnsdp_rxObj lib:libsns_dp_rx.so 
Jan 25 00:00:03 ar_logcat: ^[[31;22m[25 00:00:03.353166][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3559 !!!!!!the vif is force to dpcm duce to reduce bandwidth, if you want to use the dng, please set the pstPipeAttr->enEnabl
Jan 25 00:00:03 ar_logcat: ^[[31;22m[25 00:00:03.353193][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3603 !!!!!!! Note: your app cfg the vif write compress on,so you must cfg the vif fre >=3*mipi fre !!!!!!^[[0m
Jan 25 00:00:03 ar_logcat: ^[[31;22m[25 00:00:03.353325][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7846 not supported addr type^[[0m
Jan 25 00:00:03 ar_logcat: ^[[31;22m[25 00:00:03.353335][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7856 not supported data type^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390632][0300][0x7f812a9200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_open_camera : 920 camera_id=1^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390633][0300][0x7f8083c200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=0^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390633][0300][0x7f812a9200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390633][0300][0x7f812a9200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390633][0300][0x7f812a9200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390633][0300][0x7f8083c200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390633][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390633][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390633][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:03 ar_logcat: w h change from 0 0  to 1920 1080 fetch scaler lut 
Jan 25 00:00:03 ar_logcat: ^[[31;22m[124390633][0300][0x7f812a9200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:03 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:00:03 ar_logcat: 36e7d000 3709b000 37124000 0 0 0 371a7000 373c5000 3744e000 0 0 0 
Jan 25 00:00:03 ar_logcat: aec index 1399374359 line 1125 gain 1.000000 
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390633][0300][0x7f812a9200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390633][0300][0x7f812a9200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390633][0300][0x7f8083c200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=0 stream_id=0^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390633][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4384 power on vc 0 0^[[0m
Jan 25 00:00:03 ar_logcat: 374d7000 376f5000 3777e000 0 0 0 37801000 37a1f000 37aa8000 0 0 0 
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390635][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390636][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390636][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4386 power on vc end 0^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390643][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390643][0300][0x7f8083c200][SYS_CORE][VI_KEY] mipi_rx_9411.c: mipi_rx_power_up_9411 : 64 enable the mipi cfg and pcs clock, and set pcs to clk=100000000hz^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390643][0300][0x7f8083c200][SYS_CORE][VI_KEY] mipi_rx.c: one_mipi_init : 1204 mipi 4 init done^[[0m
Jan 25 00:00:03 ar_logcat: aec index 198685398 line 7042 gain 1.000000 
Jan 25 00:00:03 ar_logcat:  imx681_stream_on
Jan 25 00:00:03 ar_logcat:  imx681_trigger_on
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390644][0300][0x7f8083c200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390644][0300][0x7f8083c200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=0^[[0m
Jan 25 00:00:03 ar_logcat: ^[[31;22m[124390644][0300][0x7f8083c200][VI_CORE][ERROR] rx_filter_port.c: rx_filter_get_link_res : 304 pick stream err^[[0m
Jan 25 00:00:03 ar_logcat: ^[[31;22m[124390644][0300][0x7f8083c200][VI_CORE][ERROR] vif_in.c: vif_inlib_set_ctl : 304 the vif input lib [16] is already used by other vc channel^[[0m
Jan 25 00:00:03 ar_logcat: ^[[31;22m[124390644][0300][0x7f8083c200][VI_CORE][ERROR] stream.c: link_filter_external : 407 check_linking_stream error:-2146402228^[[0m
Jan 25 00:00:03 ar_logcat: ^[[31;22m[124390644][0300][0x7f8083c200][VI_CORE][ERROR] rx_filter_port.c: rx_filter_get_link_res : 304 pick stream err^[[0m
Jan 25 00:00:03 ar_logcat: ^[[31;22m[124390644][0300][0x7f8083c200][VI_CORE][ERROR] vif_in.c: vif_inlib_set_ctl : 304 the vif input lib [16] is already used by other vc channel^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390644][0300][0x7f8083c200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=1^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390644][0300][0x7f82382200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=0 stream_id=1^[[0m
Jan 25 00:00:03 ar_logcat: aec index 198685398 line 7042 gain 1.000000 
Jan 25 00:00:03 ar_logcat:  imx681_stream_on
Jan 25 00:00:03 ar_logcat:  imx681_trigger_on
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390644][0300][0x7f82382200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:03 ar_logcat: ^[[31;22m[25 00:00:03.473004][0313][0x7f657fa1d0][VI_HAL][ERROR] ar_hal_vin.c: ar_hal_vin_get_3a_info : 3625 Invalid fd -1
Jan 25 00:00:03 ar_logcat: ^[[0m
Jan 25 00:00:03 ar_logcat: ^[[31;22m[25 00:00:03.473084][0313][0x7f657fa1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetAecManuTidyAttr : 12733 isp not runing, can not set prop^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390649][0300][0x7f82382200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390650][0300][0x7f82382200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390650][0300][0x7f81616200][SYS_CORE][VI_KEY] vif_mid.c: vif_bpintr_process : 1125 stable ,ento ddr view 0^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390651][0300][0x7f81616200][SYS_CORE][VI_KEY] vif_mid.c: vif_bpintr_process : 1125 stable ,ento ddr view 2^[[0m
Jan 25 00:00:03 ar_logcat: ^[[31;22m[124390652][0498][0x7f9a960010][VPSS_MPP][ERROR] mpi_vpss.c: ar_hal_vpss_create : 870 bind_register_sender failed 0x80028003^[[0m
Jan 25 00:00:03 ar_logcat: ^[[33;22m[124390653][0300][0x7f82382200][VO_CORE][WARN] display_api.c: dc_init : 2258 use dc_reg_irq_by_cmd!^[[0m
Jan 25 00:00:03 ar_logcat: ^[[33;22m[124390653][0300][0x7f82382200][SYS_CORE][WARN] ar_subvision_ctrl.c: ar_subvision_vsync_statistic_disable : 483 subvision already disable!^[[0m
Jan 25 00:00:03 ar_logcat: ^[[31;22m[124390653][0300][0x7f82382200][VO_CORE][ERROR] display_top.c: ar_display_dev_enable : 3521 not support set irq_type:_CORE][WARN] display_api.c: dc_init : 2258 use dc_reg_irq_by_cmd!^[[0m
Jan 25 00:00:03 ar_logcat: ^[[33;22m[124390653][0300][0x7f8083c200][SYS_CORE][WARN] ar_subvision_ctrl.c: ar_subvision_vsync_statistic_disable : 483 subvision already disable!^[[0m
Jan 25 00:00:03 ar_logcat: ^[[33;22m[124390653][0300][0x7f82382200][AXIMGR_CORE][WARN] ar_aximgr_lb.c: ar_aximgr_lb_enable : 285 [LB] 1920 1024 1024^[[0m
Jan 25 00:00:03 ar_logcat: ^[[33;22m[124390653][0300][0x7f8083c200][AXIMGR_CORE][WARN] ar_aximgr_lb.c: ar_aximgr_lb_enable : 285 [LB] 1920 1024 1024^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390653][0300][0x7f808c0200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390654][0300][0x7f808c0200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390654][0300][0x7f81267200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390654][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:00:03 ar_logcat: w h change from 0 0  to 1920 1080 fetch scaler lut 
Jan 25 00:00:03 ar_logcat: ^[[31;22m[124390654][0300][0x7f81267200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:03 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:00:03 ar_logcat: 36e7d000 3709b000 37124000 0 0 0 371a7000 373c5000 3744e000 0 0 0 
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390654][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390654][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:03 ar_logcat: aec index 198685398 line 1125 gain 1.000000 
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390654][0300][0x7f81267200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390654][0300][0x7f81267200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:03 ar_logcat: w h change from 1920 1080  to 1920 1080 fetch scaler lut 
Jan 25 00:00:03 ar_logcat: ^[[31;22m[124390655][0300][0x7f81616200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:03 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:00:03 ar_logcat: 374d7000 376f5000 3777e000 0 0 0 37801000 37a1f000 37aa8000 0 0 0 
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390656][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:00:03 ar_logcat: ^[[33;22m[124390656][0300][0x7f809a5200][SYS_CORE][WARN] ar_subvision_ctrl.c: ar_subvision_vsync_statistic_enable : 456 subvision already enable!^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390657][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390657][0300][0x7f81616200][SYS_CORE][VI_KEY] vif_mid.c: vif_bpintr_process : 1173 cam_id[0], first frame done 1^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390657][0300][0x7f81616200][SYS_CORE][VI_KEY] vif_mid.c: vif_bpintr_process : 1173 cam_id[0], first frame done 1^[[0m
Jan 25 00:00:03 ar_logcat: ^[[33;22m[124390659][0300][0x7f809a5200][VO_CORE][WARN] display_top.c: display_offset_vo_src : 1520 hardware_diff_us:6635.126465 first_skewing_us:6635.126465!
Jan 25 00:00:03 ar_logcat: ^[[0m
Jan 25 00:00:03 ar_logcat: w h change from 1920 1080  to 64 36 fetch scaler lut 
Jan 25 00:00:03 ar_logcat: ^[[31;22m[124390660][0300][0x7f81616200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:03 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390661][0300][0x7f81616200][SYS_CORE][VI_KEY] vif_mid.c: vif_bpintr_process : 1173 cam_id[0], first frame done 2^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390661][0300][0x7f81616200][SYS_CORE][VI_KEY] vif_mid.c: vif_bpintr_process : 1173 cam_id[0], first frame done 2^[[0m
Jan 25 00:00:03 ar_logcat: w h change from 64 36  to 1920 1080 fetch scaler lut 
Jan 25 00:00:03 ar_logcat: ^[[31;22m[124390680][0300][0x7f81616200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:03 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:00:04 ar_logcat: ^[[33;22m[124390734][0300][0x7f81267200][AIO_CORE][WARN] i2s.c: ar_i2s_io_dev_dma_stop : 1378 s32ChnEnCnt = 0 u32ChnCnt 2.
Jan 25 00:00:04 ar_logcat: ^[[0m
Jan 25 00:00:04 ar_logcat: ^[[33;22m[124390735][0300][0x7f8081b200][AIO_CORE][WARN] i2s.c: ar_i2s_io_dev_dma_start : 1345 Ai s32ChnEnCnt = 1 u32ChnCnt 2.
Jan 25 00:00:04 ar_logcat: ^[[0m
Jan 25 00:00:05 ar_logcat: w h change from 1920 1080  to 1920 1080 fetch scaler lut 
Jan 25 00:00:05 ar_logcat: ^[[31;22m[124390806][0300][0x7f81616200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [dp_scaler_lut_direct_mode_1] failed!^[[0m
Jan 25 00:00:05 ar_logcat: yuv420 out, user have set the lut, use user lut
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390812][0300][0x7f8081b200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390813][0300][0x7f8081b200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390813][0300][0x7f80a45200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_close_camera : 961 camera_id=1^[[0m
Jan 25 00:00:07 ar_logcat: ^[[31;22m[124391007][0300][0x7f809a5200][VO_CORE][ERROR] display_top.c: display_offset_vo_src : 1510 src_de_over_time_us:-14204.160156 is more than mod_de_time_us:11110.156250(one frame), skewing_err_cnt:1^[[0m
Jan 25 00:00:07 ar_logcat: reference_clock:        300.000000Mhz
Jan 25 00:00:07 ar_logcat: src_vsync_sel:          SRC_DP_ISP
Jan 25 00:00:07 ar_logcat: de_vsync_sel:           DE1
Jan 25 00:00:07 ar_logcat: src_vsync_polarity:     0
Jan 25 00:00:07 ar_logcat: de_vsync_polarity:      0
Jan 25 00:00:07 ar_logcat: src_frame               16666.083984 us, 60.002098 fps
Jan 25 00:00:07 ar_logcat: de_frame                11110.156250 us, 90.007735 fps
Jan 25 00:00:07 ar_logcat: vsync_delay_time(limit) 8016.156738 us
Jan 25 00:00:07 ar_logcat: vsync_delay_time(over)  -14204.160156 us
Jan 25 00:00:07 ar_logcat: open the driver sns:dp_rx obj:stSnsdp_rxObj lib:libsns_dp_rx.so 
Jan 25 00:00:07 ar_logcat: ^[[31;22m[25 00:00:07.212481][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3559 !!!!!!the vif is force to dpcm duce to reduce bandwidth, if you want to use the dng, please set the pstPipeAttr->enEnabl
Jan 25 00:00:07 ar_logcat: ^[[31;22m[25 00:00:07.212515][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3603 !!!!!!! Note: your app cfg the vif write compress on,so you must cfg the vif fre >=3*mipi fre !!!!!!^[[0m
Jan 25 00:00:07 ar_logcat: ^[[31;22m[25 00:00:07.212902][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7846 not supported addr type^[[0m
Jan 25 00:00:07 ar_logcat: ^[[31;22m[25 00:00:07.212929][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7856 not supported data type^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391018][0300][0x7f80759200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_open_camera : 920 camera_id=1^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391019][0300][0x7f8083c200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391019][0300][0x7f8083c200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:07 ar_logcat: ^[[33;22m[124391019][0300][0x7f8083c200][VO_CORE][WARN] display_api.c: dc_deinit : 2323 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391019][0300][0x7f813ae200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391019][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391019][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:00:07 ar_logcat: 36e7d000 372b7000 373c7000 0 0 0 374d1000 3790b000 37a1b000 0 0 0 
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391019][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:07 ar_logcat: ^[[33;22m[124391019][0300][0x7f80759200][SYS_CORE][WARN] ar_subvision_ctrl.c: ar_subvision_vsync_statistic_disable : 483 subvision already disable!^[[0m
Jan 25 00:00:07 ar_logcat: ^[[33;22m[124391019][0300][0x7f80759200][VO_CORE][WARN] display_api.c: dc_deinit : 2323 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:00:07 ar_logcat: aec index 198685398 line 1125 gain 1.000000 
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391019][0300][0x7f813ae200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391019][0300][0x7f813ae200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391019][0300][0x7f808c0200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391020][0300][0x7f808c0200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391021][0300][0x7f808c0200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_close_camera : 961 camera_id=1^[[0m
Jan 25 00:00:07 ar_logcat: open the driver sns:dp_rx obj:stSnsdp_rxObj lib:libsns_dp_rx.so 
Jan 25 00:00:07 ar_logcat: ^[[31;22m[25 00:00:07.265367][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3559 !!!!!!the vif is force to dpcm duce to reduce bandwidth, if you want to use the dng, please set the pstPipeAttr->enEnabl
Jan 25 00:00:07 ar_logcat: ^[[31;22m[25 00:00:07.265419][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3603 !!!!!!! Note: your app cfg the vif write compress on,so you must cfg the vif fre >=3*mipi fre !!!!!!^[[0m
Jan 25 00:00:07 ar_logcat: ^[[31;22m[25 00:00:07.265550][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7846 not supported addr type^[[0m
Jan 25 00:00:07 ar_logcat: ^[[31;22m[25 00:00:07.265561][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7856 not supported data type^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391024][0300][0x7f812a9200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_open_camera : 920 camera_id=1^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391024][0300][0x7f80a45200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391024][0300][0x7f80a45200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391024][0300][0x7f82382200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391024][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:00:07 ar_logcat: 36e7d000 372b7000 373c7000 0 0 0 374d1000 3790b000 37a1b000 0 0 0 
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391024][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391024][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:07 ar_logcat: aec index 198685398 line 1125 gain 1.000000 
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391025][0300][0x7f82382200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391025][0300][0x7f82382200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:07 ar_logcat: 374d7000 37911000 37a21000 0 0 0 37b2b000 37f65000 38075000 0 0 0 
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391027][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391028][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391041][0300][0x7f8081b200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391042][0300][0x7f8081b200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:00:07 ar_logcat: ^[[31;22m[124391044][0498][0x7f6ffff1a0][VPSS_MPP][ERROR] mpi_vpss.c: ar_hal_vpss_create : 870 bind_register_sender failed 0x80028003^[[0m
Jan 25 00:00:07 ar_logcat: ^[[33;22m[124391044][0300][0x7f80738200][VO_CORE][WARN] display_api.c: dc_init : 2258 use dc_reg_irq_by_cmd!^[[0m
Jan 25 00:00:07 ar_logcat: ^[[31;22m[124391044][0300][0x7f80738200][VO_CORE][ERROR] display_top.c: ar_display_dev_enable : 3521 not support set irq_type:2m[124391044][0300][0x7f81f5f200][VO_CORE][WARN] display_api.c: dc_init : 2258 use dc_reg_irq_by_c
Jan 25 00:00:07 ar_logcat: ^[[33;22m[124391044][0300][0x7f80738200][AXIMGR_CORE][WARN] ar_aximgr_lb.c: ar_aximgr_lb_enable : 285 [LB] 1920 1024 1024^[[0m
Jan 25 00:00:07 ar_logcat: ^[[33;22m[124391044][0300][0x7f81f5f200][AXIMGR_CORE][WARN] ar_aximgr_lb.c: ar_aximgr_lb_enable : 285 [LB] 1920 1024 1024^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391044][0300][0x7f8081b200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391045][0300][0x7f8081b200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391045][0300][0x7f82382200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391045][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391045][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:00:07 ar_logcat: 36e7d000 372b7000 373c7000 0 0 0 374d1000 3790b000 37a1b000 0 0 0 
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391045][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:07 ar_logcat: 374d7000 37911000 37a21000 0 0 0 37b2b000 37f65000 38075000 0 0 0 
Jan 25 00:00:07 ar_logcat: aec index 198685398 line 1125 gain 1.000000 
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391045][0300][0x7f82382200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391045][0300][0x7f82382200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391047][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:00:07 ar_logcat: ^[[35;22m[124391048][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:00:11 ar_logcat: ^[[33;22m[124391441][0300][0x7f80a45200][VO_CORE][WARN] display_api.c: dc_deinit : 2323 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:00:11 ar_logcat: ^[[33;22m[124391441][0300][0x7f813ae200][VO_CORE][WARN] display_api.c: dc_deinit : 2323 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391441][0300][0x7f81f5f200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391442][0300][0x7f81f5f200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391443][0300][0x7f82382200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_close_camera : 961 camera_id=1^[[0m
Jan 25 00:00:11 ar_logcat: open the driver sns:dp_rx obj:stSnsdp_rxObj lib:libsns_dp_rx.so 
Jan 25 00:00:11 ar_logcat: ^[[31;22m[25 00:00:11.469399][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3559 !!!!!!the vif is force to dpcm duce to reduce bandwidth, if you want to use the dng, please set the pstPipeAttr->enEnabl
Jan 25 00:00:11 ar_logcat: ^[[31;22m[25 00:00:11.469444][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3603 !!!!!!! Note: your app cfg the vif write compress on,so you must cfg the vif fre >=3*mipi fre !!!!!!^[[0m
Jan 25 00:00:11 ar_logcat: ^[[31;22m[25 00:00:11.469585][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7846 not supported addr type^[[0m
Jan 25 00:00:11 ar_logcat: ^[[31;22m[25 00:00:11.469597][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7856 not supported data type^[[0m
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391444][0300][0x7f80759200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_open_camera : 920 camera_id=1^[[0m
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391445][0300][0x7f8081b200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391445][0300][0x7f8081b200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391445][0300][0x7f8083c200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391445][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391445][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:00:11 ar_logcat: 36e7d000 372b7000 373c7000 0 0 0 374d1000 3790b000 37a1b000 0 0 0 
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391446][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:11 ar_logcat: aec index 198685398 line 1125 gain 1.000000 
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391446][0300][0x7f8083c200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391446][0300][0x7f8083c200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:11 ar_logcat: 374d7000 37911000 37a21000 0 0 0 37b2b000 37f65000 38075000 0 0 0 
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391448][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391450][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391462][0300][0x7f813ae200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391463][0300][0x7f813ae200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:00:11 ar_logcat: ^[[31;22m[124391464][0498][0x7ef28781a0][VPSS_MPP][ERROR] mpi_vpss.c: ar_hal_vpss_create : 870 bind_register_sender failed 0x80028003^[[0m
Jan 25 00:00:11 ar_logcat: ^[[33;22m[124391464][0300][0x7f80a45200][VO_CORE][WARN] display_api.c: dc_init : 2258 use dc_reg_irq_by_cmd!^[[0m
Jan 25 00:00:11 ar_logcat: ^[[31;22m[124391464][0300][0x7f80a45200][VO_CORE][ERROR] display_top.c: ar_display_dev_enable : 3521 not support set irq_type::11110.156250(one frame), skewing_err_cnt:1^[[0m
Jan 25 00:00:11 ar_logcat: RE][WARN] ar_aximgr_lb.c: ar_aximgr_lb_enable : 285 [LB] 1920 1024 1024^[[0m
Jan 25 00:00:11 ar_logcat: ^[[33;22m[124391465][0300][0x7f80a45200][AXIMGR_CORE][WARN] ar_aximgr_lb.c: ar_aximgr_lb_enable : 285 [LB] 1920 1024 1024^[[0m
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391465][0300][0x7f8083c200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391465][0300][0x7f8083c200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391466][0300][0x7f8081b200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391466][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:00:11 ar_logcat: 36e7d000 372b7000 373c7000 0 0 0 374d1000 3790b000 37a1b000 0 0 0 
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391466][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391466][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:11 ar_logcat: aec index 198685398 line 1125 gain 1.000000 
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391466][0300][0x7f8081b200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391466][0300][0x7f8081b200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:11 ar_logcat: 374d7000 37911000 37a21000 0 0 0 37b2b000 37f65000 38075000 0 0 0 
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391468][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:00:11 ar_logcat: ^[[35;22m[124391470][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:01:06 ar_logcat: ^[[33;22m[124396937][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:01:06 ar_logcat: ^[[0m
Jan 25 00:01:24 ar_logcat: ^[[33;22m[124398697][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:01:24 ar_logcat: ^[[0m
Jan 25 00:01:37 ar_logcat: ^[[33;22m[124400004][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:01:37 ar_logcat: ^[[0m
Jan 25 00:02:26 ar_logcat: ^[[33;22m[124404986][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:02:26 ar_logcat: ^[[0m
Jan 25 00:02:41 ar_logcat: ^[[33;22m[124406406][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:02:41 ar_logcat: ^[[0m
Jan 25 00:02:57 ar_logcat: ^[[33;22m[124408020][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:02:57 ar_logcat: ^[[0m
Jan 25 00:02:57 ar_logcat: ^[[33;22m[124408046][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:02:57 ar_logcat: ^[[0m
Jan 25 00:03:29 ar_logcat: ^[[33;22m[124411214][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:03:29 ar_logcat: ^[[0m
Jan 25 00:03:36 ar_logcat: ^[[33;22m[124411994][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:03:36 ar_logcat: ^[[0m
Jan 25 00:03:45 ar_logcat: ^[[33;22m[124412814][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:03:45 ar_logcat: ^[[0m
Jan 25 00:03:53 ar_logcat: ^[[33;22m[124413621][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:03:53 ar_logcat: ^[[0m
Jan 25 00:03:54 ar_logcat: ^[[33;22m[124413755][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:03:54 ar_logcat: ^[[0m
Jan 25 00:03:58 ar_logcat: ^[[33;22m[124414165][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:03:58 ar_logcat: ^[[0m
Jan 25 00:03:59 ar_logcat: ^[[33;22m[124414271][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:03:59 ar_logcat: ^[[0m
Jan 25 00:03:59 ar_logcat: ^[[33;22m[124414282][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:03:59 ar_logcat: ^[[0m
Jan 25 00:04:02 ar_logcat: ^[[33;22m[124414535][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:04:02 ar_logcat: ^[[0m
Jan 25 00:04:02 ar_logcat: ^[[33;22m[124414545][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:04:02 ar_logcat: ^[[0m
Jan 25 00:04:08 ar_logcat: ^[[33;22m[124415132][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:04:08 ar_logcat: ^[[0m
Jan 25 00:04:10 ar_logcat: ^[[33;22m[124415329][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:04:10 ar_logcat: ^[[0m
Jan 25 00:04:13 ar_logcat: ^[[33;22m[124415632][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:04:13 ar_logcat: ^[[0m
Jan 25 00:04:21 ar_logcat: ^[[33;22m[124416480][0300][0x7f8081b200][VO_CORE][WARN] display_api.c: dc_deinit : 2323 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:04:21 ar_logcat: ^[[33;22m[124416480][0300][0x7f813ae200][VO_CORE][WARN] display_api.c: dc_deinit : 2323 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:04:21 ar_logcat: ^[[35;22m[124416480][0300][0x7f80759200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:04:21 ar_logcat: ^[[35;22m[124416480][0300][0x7f80759200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:04:21 ar_logcat: ^[[35;22m[124416480][0300][0x7f82382200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_close_camera : 961 camera_id=1^[[0m
Jan 25 00:04:21 ar_logcat: open the driver sns:dp_rx obj:stSnsdp_rxObj lib:libsns_dp_rx.so 
Jan 25 00:04:21 ar_logcat: ^[[31;22m[25 00:04:21.854423][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3559 !!!!!!the vif is force to dpcm duce to reduce bandwidth, if you want to use the dng, please set the pstPipeAttr->enEnabl
Jan 25 00:04:21 ar_logcat: ^[[31;22m[25 00:04:21.854498][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3603 !!!!!!! Note: your app cfg the vif write compress on,so you must cfg the vif fre >=3*mipi fre !!!!!!^[[0m
Jan 25 00:04:21 ar_logcat: ^[[31;22m[25 00:04:21.854742][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7846 not supported addr type^[[0m
Jan 25 00:04:21 ar_logcat: ^[[31;22m[25 00:04:21.854758][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7856 not supported data type^[[0m
Jan 25 00:04:21 ar_logcat: ^[[35;22m[124416483][0300][0x7f80738200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_open_camera : 920 camera_id=1^[[0m
Jan 25 00:04:21 ar_logcat: ^[[35;22m[124416483][0300][0x7f806f6200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:04:21 ar_logcat: ^[[35;22m[124416483][0300][0x7f806f6200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:04:21 ar_logcat: ^[[35;22m[124416483][0300][0x7f812a9200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:04:21 ar_logcat: ^[[35;22m[124416483][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:04:21 ar_logcat: 36e7d000 372b7000 373c7000 0 0 0 374d1000 3790b000 37a1b000 0 0 0 
Jan 25 00:04:21 ar_logcat: ^[[35;22m[124416483][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:04:21 ar_logcat: ^[[35;22m[124416483][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:04:21 ar_logcat: aec index 198685398 line 1125 gain 1.000000 
Jan 25 00:04:21 ar_logcat: ^[[35;22m[124416484][0300][0x7f812a9200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:04:21 ar_logcat: ^[[35;22m[124416484][0300][0x7f812a9200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:04:21 ar_logcat: 374d7000 37911000 37a21000 0 0 0 37b2b000 37f65000 38075000 0 0 0 
Jan 25 00:04:21 ar_logcat: ^[[35;22m[124416486][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:04:21 ar_logcat: ^[[35;22m[124416487][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:04:22 ar_logcat: ^[[35;22m[124416500][0300][0x7f81267200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:04:22 ar_logcat: ^[[35;22m[124416500][0300][0x7f81267200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:04:22 ar_logcat: ^[[31;22m[124416503][0498][0x7ef28781a0][VPSS_MPP][ERROR] mpi_vpss.c: ar_hal_vpss_create : 870 bind_register_sender failed 0x80028003^[[0m
Jan 25 00:04:22 ar_logcat: ^[[33;22m[124416503][0300][0x7f80759200][VO_CORE][WARN] display_api.c: dc_init : 2258 use dc_reg_irq_by_cmd!^[[0m
Jan 25 00:04:22 ar_logcat: ^[[31;22m[124416503][0300][0x7f80759200][VO_CORE][ERROR] display_top.c: ar_display_dev_enable : 3521 not support set irq_type:2m[124416503][0300][0x7f812a9200][VO_CORE][WARN] display_api.c: dc_init : 2258 use dc_reg_irq_by_c
Jan 25 00:04:22 ar_logcat: ^[[33;22m[124416503][0300][0x7f81267200][AXIMGR_CORE][WARN] ar_aximgr_lb.c: ar_aximgr_lb_enable : 285 [LB] 1920 1024 1024^[[0m
Jan 25 00:04:22 ar_logcat: ^[[33;22m[124416503][0300][0x7f80759200][AXIMGR_CORE][WARN] ar_aximgr_lb.c: ar_aximgr_lb_enable : 285 [LB] 1920 1024 1024^[[0m
Jan 25 00:04:22 ar_logcat: ^[[35;22m[124416503][0300][0x7f82382200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:04:22 ar_logcat: ^[[35;22m[124416503][0300][0x7f82382200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:04:22 ar_logcat: ^[[35;22m[124416504][0300][0x7f80759200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:04:22 ar_logcat: ^[[35;22m[124416504][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:04:22 ar_logcat: 36e7d000 372b7000 373c7000 0 0 0 374d1000 3790b000 37a1b000 0 0 0 
Jan 25 00:04:22 ar_logcat: 374d7000 37911000 37a21000 0 0 0 37b2b000 37f65000 38075000 0 0 0 
Jan 25 00:04:22 ar_logcat: ^[[35;22m[124416504][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:04:22 ar_logcat: ^[[35;22m[124416504][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:04:22 ar_logcat: aec index 198685398 line 1125 gain 1.000000 
Jan 25 00:04:22 ar_logcat: ^[[35;22m[124416504][0300][0x7f80759200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:04:22 ar_logcat: ^[[35;22m[124416504][0300][0x7f80759200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:04:22 ar_logcat: ^[[35;22m[124416506][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:04:22 ar_logcat: ^[[35;22m[124416507][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:04:22 ar_logcat: ^[[33;22m[124416510][0300][0x7f80738200][VO_CORE][WARN] display_api.c: dc_deinit : 2323 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:04:22 ar_logcat: ^[[33;22m[124416510][0300][0x7f808e1200][VO_CORE][WARN] display_api.c: dc_deinit : 2323 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:04:22 ar_logcat: ^[[35;22m[124416510][0300][0x7f82382200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:04:22 ar_logcat: ^[[35;22m[124416510][0300][0x7f82382200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:04:22 ar_logcat: ^[[35;22m[124416511][0300][0x7f80759200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_close_camera : 961 camera_id=1^[[0m
Jan 25 00:04:22 ar_logcat: open the driver sns:dp_rx obj:stSnsdp_rxObj lib:libsns_dp_rx.so 
Jan 25 00:04:22 ar_logcat: ^[[31;22m[25 00:04:22.158570][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3559 !!!!!!the vif is force to dpcm duce to reduce bandwidth, if you want to use the dng, please set the pstPipeAttr->enEnabl
Jan 25 00:04:22 ar_logcat: ^[[31;22m[25 00:04:22.158601][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3603 !!!!!!! Note: your app cfg the vif write compress on,so you must cfg the vif fre >=3*mipi fre !!!!!!^[[0m
Jan 25 00:04:22 ar_logcat: ^[[31;22m[25 00:04:22.158744][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7846 not supported addr type^[[0m
Jan 25 00:04:22 ar_logcat: ^[[31;22m[25 00:04:22.158757][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7856 not supported data type^[[0m
Jan 25 00:04:22 ar_logcat: ^[[35;22m[124416513][0300][0x7f8081b200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_open_camera : 920 camera_id=1^[[0m
Jan 25 00:04:22 ar_logcat: ^[[35;22m[124416513][0300][0x7f80738200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:04:22 ar_logcat: ^[[35;22m[124416513][0300][0x7f80738200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:04:22 ar_logcat: ^[[35;22m[124416514][0300][0x7f80759200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:04:22 ar_logcat: ^[[35;22m[124416514][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:04:22 ar_logcat: 36e7d000 372b7000 373c7000 0 0 0 374d1000 3790b000 37a1b000 0 0 0 
Jan 25 00:04:22 ar_logcat: ^[[35;22m[124416514][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:04:22 ar_logcat: ^[[35;22m[124416514][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:04:22 ar_logcat: 374d7000 37911000 37a21000 0 0 0 37b2b000 37f65000 38075000 0 0 0 
Jan 25 00:04:22 ar_logcat: aec index 198685398 line 1125 gain 1.000000 
Jan 25 00:04:22 ar_logcat: ^[[35;22m[124416514][0300][0x7f80759200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:04:22 ar_logcat: ^[[35;22m[124416514][0300][0x7f80759200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:04:22 ar_logcat: ^[[35;22m[124416516][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:04:22 ar_logcat: ^[[35;22m[124416517][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:04:22 ar_logcat: ^[[35;22m[124416530][0300][0x7f8081b200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:04:22 ar_logcat: ^[[35;22m[124416530][0300][0x7f8081b200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:04:22 ar_logcat: ^[[31;22m[124416533][0498][0x7ef28781a0][VPSS_MPP][ERROR] mpi_vpss.c: ar_hal_vpss_create : 870 bind_register_sender failed 0x80028003^[[0m
Jan 25 00:04:22 ar_logcat: ^[[33;22m[124416533][0300][0x7f8081b200][VO_CORE][WARN] display_api.c: dc_init : 2258 use dc_reg_irq_by_cmd!^[[0m
Jan 25 00:04:22 ar_logcat: ^[[31;22m[124416533][0300][0x7f8081b200][VO_CORE][ERROR] display_top.c: ar_display_dev_enable : 3521 not support set irq_type:k : 2033 camera_id=1^[[0m
Jan 25 00:04:22 ar_logcat: ^[[35;22m[124416534][0300][0x7f81267200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:04:22 ar_logcat: ^[[35;22m[124416534][0300][0x7f813ae200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:04:22 ar_logcat: ^[[35;22m[124416535][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:04:22 ar_logcat: ^[[35;22m[124416535][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:04:22 ar_logcat: ^[[35;22m[124416535][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:04:22 ar_logcat: 36e7d000 372b7000 373c7000 0 0 0 374d1000 3790b000 37a1b000 0 0 0 
Jan 25 00:04:22 ar_logcat: aec index 198685398 line 1125 gain 1.000000 
Jan 25 00:04:22 ar_logcat: ^[[35;22m[124416535][0300][0x7f813ae200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:04:22 ar_logcat: ^[[35;22m[124416535][0300][0x7f813ae200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:04:22 ar_logcat: 374d7000 37911000 37a21000 0 0 0 37b2b000 37f65000 38075000 0 0 0 
Jan 25 00:04:22 ar_logcat: ^[[35;22m[124416537][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:04:22 ar_logcat: ^[[35;22m[124416539][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:04:35 ar_logcat: ^[[33;22m[124417829][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:04:35 ar_logcat: ^[[0m
Jan 25 00:04:35 ar_logcat: ^[[33;22m[124417856][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:04:35 ar_logcat: ^[[0m
Jan 25 00:04:35 ar_logcat: ^[[33;22m[124417869][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:04:35 ar_logcat: ^[[0m
Jan 25 00:04:36 ar_logcat: ^[[33;22m[124417936][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:04:36 ar_logcat: ^[[0m
Jan 25 00:04:36 ar_logcat: ^[[33;22m[124417949][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:04:36 ar_logcat: ^[[0m
Jan 25 00:04:38 ar_logcat: ^[[33;22m[124418109][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:04:38 ar_logcat: ^[[0m
Jan 25 00:04:43 ar_logcat: ^[[33;22m[124418600][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:04:43 ar_logcat: ^[[0m
Jan 25 00:04:43 ar_logcat: ^[[33;22m[124418640][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:04:43 ar_logcat: ^[[0m
Jan 25 00:04:53 ar_logcat: ^[[33;22m[124419603][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:04:53 ar_logcat: ^[[0m
Jan 25 00:04:56 ar_logcat: ^[[33;22m[124419920][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:04:56 ar_logcat: ^[[0m
Jan 25 00:04:58 ar_logcat: ^[[33;22m[124420147][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:04:58 ar_logcat: ^[[0m
Jan 25 00:04:59 ar_logcat: ^[[33;22m[124420213][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:04:59 ar_logcat: ^[[0m
Jan 25 00:05:03 ar_logcat: ^[[33;22m[124420677][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:05:03 ar_logcat: ^[[0m
Jan 25 00:05:07 ar_logcat: ^[[33;22m[124421007][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:05:07 ar_logcat: ^[[0m
Jan 25 00:05:11 ar_logcat: ^[[33;22m[124421377][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:05:11 ar_logcat: ^[[0m
Jan 25 00:05:20 ar_logcat: ^[[33;22m[124422291][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:05:20 ar_logcat: ^[[0m
Jan 25 00:05:21 ar_logcat: ^[[33;22m[124422384][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:05:21 ar_logcat: ^[[0m
Jan 25 00:05:21 ar_logcat: ^[[33;22m[124422394][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:05:21 ar_logcat: ^[[0m
Jan 25 00:05:33 ar_logcat: ^[[33;22m[124423598][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:05:33 ar_logcat: ^[[0m
Jan 25 00:05:43 ar_logcat: ^[[33;22m[124424551][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:05:43 ar_logcat: ^[[0m
Jan 25 00:05:45 ar_logcat: ^[[33;22m[124424778][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:05:45 ar_logcat: ^[[0m
Jan 25 00:05:47 ar_logcat: ^[[33;22m[124425028][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:05:47 ar_logcat: ^[[0m
Jan 25 00:05:57 ar_logcat: ^[[33;22m[124426022][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:05:57 ar_logcat: ^[[0m
Jan 25 00:06:03 ar_logcat: ^[[33;22m[124426642][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:03 ar_logcat: ^[[0m
Jan 25 00:06:03 ar_logcat: ^[[33;22m[124426645][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:03 ar_logcat: ^[[0m
Jan 25 00:06:04 ar_logcat: ^[[33;22m[124426659][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:04 ar_logcat: ^[[0m
Jan 25 00:06:04 ar_logcat: ^[[33;22m[124426672][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:04 ar_logcat: ^[[0m
Jan 25 00:06:04 ar_logcat: ^[[33;22m[124426695][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:04 ar_logcat: ^[[0m
Jan 25 00:06:05 ar_logcat: ^[[33;22m[124426789][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:05 ar_logcat: ^[[0m
Jan 25 00:06:05 ar_logcat: ^[[33;22m[124426802][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:05 ar_logcat: ^[[0m
Jan 25 00:06:05 ar_logcat: ^[[33;22m[124426829][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:05 ar_logcat: ^[[0m
Jan 25 00:06:10 ar_logcat: ^[[33;22m[124427319][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:10 ar_logcat: ^[[0m
Jan 25 00:06:13 ar_logcat: ^[[33;22m[124427622][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:13 ar_logcat: ^[[0m
Jan 25 00:06:14 ar_logcat: ^[[33;22m[124427662][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:14 ar_logcat: ^[[0m
Jan 25 00:06:15 ar_logcat: ^[[33;22m[124427782][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:15 ar_logcat: ^[[0m
Jan 25 00:06:21 ar_logcat: ^[[33;22m[124428443][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:21 ar_logcat: ^[[0m
Jan 25 00:06:33 ar_logcat: ^[[33;22m[124429650][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:33 ar_logcat: ^[[0m
Jan 25 00:06:38 ar_logcat: ^[[33;22m[124430100][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:38 ar_logcat: ^[[0m
Jan 25 00:06:40 ar_logcat: ^[[33;22m[124430347][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:40 ar_logcat: ^[[0m
Jan 25 00:06:53 ar_logcat: ^[[33;22m[124431647][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:53 ar_logcat: ^[[0m
Jan 25 00:06:54 ar_logcat: ^[[33;22m[124431687][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:54 ar_logcat: ^[[0m
Jan 25 00:06:54 ar_logcat: ^[[33;22m[124431700][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:54 ar_logcat: ^[[0m
Jan 25 00:06:55 ar_logcat: ^[[33;22m[124431780][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:55 ar_logcat: ^[[0m
Jan 25 00:06:56 ar_logcat: ^[[33;22m[124431897][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:56 ar_logcat: ^[[0m
Jan 25 00:07:07 ar_logcat: ^[[33;22m[124433044][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:07:07 ar_logcat: ^[[0m
Jan 25 00:07:08 ar_logcat: ^[[33;22m[124433061][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:07:08 ar_logcat: ^[[0m
Jan 25 00:07:08 ar_logcat: ^[[33;22m[124433064][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:07:08 ar_logcat: ^[[0m
Jan 25 00:07:12 ar_logcat: ^[[33;22m[124433454][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:07:12 ar_logcat: ^[[0m
Jan 25 00:07:12 ar_logcat: ^[[33;22m[124433458][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:07:12 ar_logcat: ^[[0m
Jan 25 00:07:12 ar_logcat: ^[[33;22m[124433471][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:07:12 ar_logcat: ^[[0m
Jan 25 00:07:12 ar_logcat: ^[[33;22m[124433484][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:07:12 ar_logcat: ^[[0m
Jan 25 00:07:16 ar_logcat: ^[[33;22m[124433911][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:07:16 ar_logcat: ^[[0m
Jan 25 00:07:19 ar_logcat: ^[[33;22m[124434214][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:07:19 ar_logcat: ^[[0m
Jan 25 00:07:19 ar_logcat: ^[[33;22m[124434238][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:07:19 ar_logcat: ^[[0m
Jan 25 00:07:24 ar_logcat: ^[[33;22m[124434661][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:07:24 ar_logcat: ^[[0m
Jan 25 00:07:24 ar_logcat: ^[[33;22m[124434665][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:07:24 ar_logcat: ^[[0m
Jan 25 00:07:24 ar_logcat: ^[[33;22m[124434678][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:07:24 ar_logcat: ^[[0m
Jan 25 00:07:28 ar_logcat: ^[[33;22m[124435141][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:07:28 ar_logcat: ^[[0m
Jan 25 00:07:32 ar_logcat: ^[[33;22m[124435485][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:07:32 ar_logcat: ^[[0m
Jan 25 00:07:36 ar_logcat: ^[[33;22m[124435935][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:07:36 ar_logcat: ^[[0m
Jan 25 00:07:41 ar_logcat: ^[[33;22m[124436372][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:07:41 ar_logcat: ^[[0m
Jan 25 00:07:50 ar_logcat: ^[[33;22m[124437295][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:07:50 ar_logcat: ^[[0m
Jan 25 00:07:50 ar_logcat: ^[[33;22m[124437299][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:07:50 ar_logcat: ^[[0m
Jan 25 00:07:52 ar_logcat: ^[[33;22m[124437535][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:07:52 ar_logcat: ^[[0m
Jan 25 00:07:53 ar_logcat: ^[[33;22m[124437589][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:07:53 ar_logcat: ^[[0m
Jan 25 00:07:58 ar_logcat: ^[[33;22m[124438142][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:07:58 ar_logcat: ^[[0m
Jan 25 00:07:59 ar_logcat: ^[[33;22m[124438186][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:07:59 ar_logcat: ^[[0m
Jan 25 00:07:59 ar_logcat: ^[[33;22m[124438226][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:07:59 ar_logcat: ^[[0m
Jan 25 00:08:03 ar_logcat: ^[[33;22m[124438609][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:03 ar_logcat: ^[[0m
Jan 25 00:08:04 ar_logcat: ^[[33;22m[124438742][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:04 ar_logcat: ^[[0m
Jan 25 00:08:12 ar_logcat: ^[[33;22m[124439523][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:12 ar_logcat: ^[[0m
Jan 25 00:08:23 ar_logcat: ^[[33;22m[124440633][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:23 ar_logcat: ^[[0m
Jan 25 00:08:26 ar_logcat: ^[[33;22m[124440860][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:26 ar_logcat: ^[[0m
Jan 25 00:08:26 ar_logcat: ^[[33;22m[124440873][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:26 ar_logcat: ^[[0m
Jan 25 00:08:27 ar_logcat: ^[[33;22m[124440990][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:27 ar_logcat: ^[[0m
Jan 25 00:08:31 ar_logcat: ^[[33;22m[124441363][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:31 ar_logcat: ^[[0m
Jan 25 00:08:31 ar_logcat: ^[[33;22m[124441403][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:31 ar_logcat: ^[[0m
Jan 25 00:08:31 ar_logcat: ^[[33;22m[124441416][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:31 ar_logcat: ^[[0m
Jan 25 00:08:31 ar_logcat: ^[[33;22m[124441430][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:31 ar_logcat: ^[[0m
Jan 25 00:08:31 ar_logcat: ^[[33;22m[124441443][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:31 ar_logcat: ^[[0m
Jan 25 00:08:41 ar_logcat: ^[[33;22m[124442383][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:41 ar_logcat: ^[[0m
Jan 25 00:08:47 ar_logcat: ^[[33;22m[124443017][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:47 ar_logcat: ^[[0m
Jan 25 00:08:53 ar_logcat: ^[[33;22m[124443627][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:53 ar_logcat: ^[[0m
Jan 25 00:08:54 ar_logcat: ^[[33;22m[124443681][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:54 ar_logcat: ^[[0m
Jan 25 00:08:54 ar_logcat: ^[[33;22m[124443721][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:54 ar_logcat: ^[[0m
Jan 25 00:08:55 ar_logcat: ^[[33;22m[124443811][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:55 ar_logcat: ^[[0m
Jan 25 00:08:55 ar_logcat: ^[[33;22m[124443827][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:55 ar_logcat: ^[[0m
Jan 25 00:08:56 ar_logcat: ^[[33;22m[124443894][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:56 ar_logcat: ^[[0m
Jan 25 00:08:57 ar_logcat: ^[[33;22m[124444024][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:57 ar_logcat: ^[[0m
Jan 25 00:08:57 ar_logcat: ^[[33;22m[124444051][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:57 ar_logcat: ^[[0m
Jan 25 00:08:58 ar_logcat: ^[[33;22m[124444091][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:58 ar_logcat: ^[[0m
Jan 25 00:09:15 ar_logcat: ^[[33;22m[124445811][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:09:15 ar_logcat: ^[[0m
Jan 25 00:09:17 ar_logcat: ^[[33;22m[124446048][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:09:17 ar_logcat: ^[[0m
Jan 25 00:09:17 ar_logcat: ^[[33;22m[124446051][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:09:17 ar_logcat: ^[[0m
Jan 25 00:09:36 ar_logcat: ^[[33;22m[124447929][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:09:36 ar_logcat: ^[[0m
Jan 25 00:09:40 ar_logcat: ^[[33;22m[124448339][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:09:40 ar_logcat: ^[[0m
Jan 25 00:09:41 ar_logcat: ^[[33;22m[124448382][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:09:41 ar_logcat: ^[[0m
Jan 25 00:09:58 ar_logcat: ^[[33;22m[124450116][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:09:58 ar_logcat: ^[[0m
Jan 25 00:09:58 ar_logcat: ^[[33;22m[124450139][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:09:58 ar_logcat: ^[[0m
Jan 25 00:10:09 ar_logcat: ^[[33;22m[124451173][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:09 ar_logcat: ^[[0m
Jan 25 00:10:09 ar_logcat: ^[[33;22m[124451186][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:09 ar_logcat: ^[[0m
Jan 25 00:10:09 ar_logcat: ^[[33;22m[124451239][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:09 ar_logcat: ^[[0m
Jan 25 00:10:11 ar_logcat: ^[[33;22m[124451360][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:11 ar_logcat: ^[[0m
Jan 25 00:10:11 ar_logcat: ^[[33;22m[124451370][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:11 ar_logcat: ^[[0m
Jan 25 00:10:11 ar_logcat: ^[[33;22m[124451413][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:11 ar_logcat: ^[[0m
Jan 25 00:10:11 ar_logcat: ^[[33;22m[124451426][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:11 ar_logcat: ^[[0m
Jan 25 00:10:11 ar_logcat: ^[[33;22m[124451453][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:11 ar_logcat: ^[[0m
Jan 25 00:10:12 ar_logcat: ^[[33;22m[124451466][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:12 ar_logcat: ^[[0m
Jan 25 00:10:14 ar_logcat: ^[[33;22m[124451690][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:14 ar_logcat: ^[[0m
Jan 25 00:10:14 ar_logcat: ^[[31;22m[124451690][0300][0x7f823e5200][AIO_CORE][ERROR] i2s.c: ar_i2s_dma_isr : 413 [ahb-4] i2s int blk error: next:0x3cc1d039 h:0x200 l:0x1a209425.
Jan 25 00:10:14 ar_logcat: ^[[0m
Jan 25 00:10:24 ar_logcat: ^[[33;22m[124452720][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:24 ar_logcat: ^[[0m
Jan 25 00:10:25 ar_logcat: ^[[33;22m[124452763][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:25 ar_logcat: ^[[0m
Jan 25 00:10:25 ar_logcat: ^[[33;22m[124452787][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:25 ar_logcat: ^[[0m
Jan 25 00:10:27 ar_logcat: ^[[33;22m[124453027][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:27 ar_logcat: ^[[0m
Jan 25 00:10:34 ar_logcat: ^[[33;22m[124453727][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:34 ar_logcat: ^[[0m
Jan 25 00:10:39 ar_logcat: ^[[33;22m[124454218][0300][0x7f80738200][VO_CORE][WARN] display_api.c: dc_deinit : 2323 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:10:39 ar_logcat: ^[[33;22m[124454218][0300][0x7f8081b200][VO_CORE][WARN] display_api.c: dc_deinit : 2323 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:10:39 ar_logcat: ^[[35;22m[124454218][0300][0x7f82382200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:10:39 ar_logcat: ^[[35;22m[124454218][0300][0x7f82382200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:10:39 ar_logcat: ^[[35;22m[124454219][0300][0x7f8081b200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_close_camera : 961 camera_id=1^[[0m
Jan 25 00:10:39 ar_logcat: open the driver sns:dp_rx obj:stSnsdp_rxObj lib:libsns_dp_rx.so 
Jan 25 00:10:39 ar_logcat: ^[[31;22m[25 00:10:39.235448][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3559 !!!!!!the vif is force to dpcm duce to reduce bandwidth, if you want to use the dng, please set the pstPipeAttr->enEnabl
Jan 25 00:10:39 ar_logcat: ^[[31;22m[25 00:10:39.235482][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3603 !!!!!!! Note: your app cfg the vif write compress on,so you must cfg the vif fre >=3*mipi fre !!!!!!^[[0m
Jan 25 00:10:39 ar_logcat: ^[[31;22m[25 00:10:39.235642][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7846 not supported addr type^[[0m
Jan 25 00:10:39 ar_logcat: ^[[31;22m[25 00:10:39.235655][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7856 not supported data type^[[0m
Jan 25 00:10:39 ar_logcat: ^[[35;22m[124454221][0300][0x7f806f6200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_open_camera : 920 camera_id=1^[[0m
Jan 25 00:10:39 ar_logcat: ^[[35;22m[124454221][0300][0x7f80738200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:10:39 ar_logcat: ^[[35;22m[124454221][0300][0x7f80738200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:10:39 ar_logcat: ^[[35;22m[124454221][0300][0x7f808c0200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:10:39 ar_logcat: ^[[35;22m[124454221][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:10:39 ar_logcat: ^[[35;22m[124454221][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:10:39 ar_logcat: 36e7d000 372b7000 373c7000 0 0 0 374d1000 3790b000 37a1b000 0 0 0 
Jan 25 00:10:39 ar_logcat: ^[[35;22m[124454221][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:10:39 ar_logcat: aec index 198685398 line 1125 gain 1.000000 
Jan 25 00:10:39 ar_logcat: ^[[35;22m[124454222][0300][0x7f808c0200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:10:39 ar_logcat: ^[[35;22m[124454222][0300][0x7f808c0200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:10:39 ar_logcat: 374d7000 37911000 37a21000 0 0 0 37b2b000 37f65000 38075000 0 0 0 
Jan 25 00:10:39 ar_logcat: ^[[35;22m[124454224][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:10:39 ar_logcat: ^[[35;22m[124454226][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:10:39 ar_logcat: ^[[35;22m[124454238][0300][0x7f813ae200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:10:39 ar_logcat: ^[[35;22m[124454238][0300][0x7f813ae200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:10:39 ar_logcat: ^[[31;22m[124454241][0498][0x7ef28781a0][VPSS_MPP][ERROR] mpi_vpss.c: ar_hal_vpss_create : 870 bind_register_sender failed 0x80028003^[[0m
Jan 25 00:10:39 ar_logcat: ^[[33;22m[124454241][0300][0x7f813ae200][VO_CORE][WARN] display_api.c: dc_init : 2258 use dc_reg_irq_by_cmd!^[[0m
Jan 25 00:10:39 ar_logcat: ^[[31;22m[124454241][0300][0x7f813ae200][VO_CORE][ERROR] display_top.c: ar_display_dev_enable : 3521 not support set irq_type::11110.156250(one frame), skewing_err_cnt:1^[[0m
Jan 25 00:10:39 ar_logcat: RE][WARN] ar_aximgr_lb.c: ar_aximgr_lb_enable : 285 [LB] 1920 1024 1024^[[0m
Jan 25 00:10:39 ar_logcat: ^[[33;22m[124454241][0300][0x7f812a9200][AXIMGR_CORE][WARN] ar_aximgr_lb.c: ar_aximgr_lb_enable : 285 [LB] 1920 1024 1024^[[0m
Jan 25 00:10:39 ar_logcat: ^[[35;22m[124454242][0300][0x7f808e1200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:10:39 ar_logcat: ^[[35;22m[124454242][0300][0x7f808e1200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:10:39 ar_logcat: ^[[35;22m[124454242][0300][0x7f808c0200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:10:39 ar_logcat: ^[[35;22m[124454242][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:10:39 ar_logcat: ^[[35;22m[124454242][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:10:39 ar_logcat: 36e7d000 372b7000 373c7000 0 0 0 374d1000 3790b000 37a1b000 0 0 0 
Jan 25 00:10:39 ar_logcat: ^[[35;22m[124454242][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:10:39 ar_logcat: aec index 198685398 line 1125 gain 1.000000 
Jan 25 00:10:39 ar_logcat: ^[[35;22m[124454242][0300][0x7f808c0200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:10:39 ar_logcat: ^[[35;22m[124454242][0300][0x7f808c0200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:10:39 ar_logcat: 374d7000 37911000 37a21000 0 0 0 37b2b000 37f65000 38075000 0 0 0 
Jan 25 00:10:39 ar_logcat: ^[[35;22m[124454244][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:10:39 ar_logcat: ^[[35;22m[124454246][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:10:39 ar_logcat: ^[[33;22m[124454249][0300][0x7f808c0200][VO_CORE][WARN] display_api.c: dc_deinit : 2323 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:10:39 ar_logcat: ^[[33;22m[124454250][0300][0x7f80759200][VO_CORE][WARN] display_api.c: dc_deinit : 2323 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:10:39 ar_logcat: ^[[35;22m[124454250][0300][0x7f81f5f200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:10:39 ar_logcat: ^[[35;22m[124454251][0300][0x7f81f5f200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:10:39 ar_logcat: ^[[35;22m[124454252][0300][0x7f80759200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_close_camera : 961 camera_id=1^[[0m
Jan 25 00:10:39 ar_logcat: open the driver sns:dp_rx obj:stSnsdp_rxObj lib:libsns_dp_rx.so 
Jan 25 00:10:39 ar_logcat: ^[[31;22m[25 00:10:39.552889][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3559 !!!!!!the vif is force to dpcm duce to reduce bandwidth, if you want to use the dng, please set the pstPipeAttr->enEnabl
Jan 25 00:10:39 ar_logcat: ^[[31;22m[25 00:10:39.552931][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3603 !!!!!!! Note: your app cfg the vif write compress on,so you must cfg the vif fre >=3*mipi fre !!!!!!^[[0m
Jan 25 00:10:39 ar_logcat: ^[[31;22m[25 00:10:39.553113][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7846 not supported addr type^[[0m
Jan 25 00:10:39 ar_logcat: ^[[31;22m[25 00:10:39.553152][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7856 not supported data type^[[0m
Jan 25 00:10:39 ar_logcat: ^[[35;22m[124454252][0300][0x7f812a9200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_open_camera : 920 camera_id=1^[[0m
Jan 25 00:10:39 ar_logcat: ^[[35;22m[124454253][0300][0x7f81f5f200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:10:39 ar_logcat: ^[[35;22m[124454253][0300][0x7f81f5f200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:10:39 ar_logcat: ^[[35;22m[124454253][0300][0x7f812a9200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:10:39 ar_logcat: ^[[35;22m[124454253][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:10:39 ar_logcat: 36e7d000 372b7000 373c7000 0 0 0 374d1000 3790b000 37a1b000 0 0 0 
Jan 25 00:10:39 ar_logcat: ^[[35;22m[124454253][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:10:39 ar_logcat: ^[[35;22m[124454254][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:10:39 ar_logcat: aec index 198685398 line 1125 gain 1.000000 
Jan 25 00:10:39 ar_logcat: ^[[35;22m[124454254][0300][0x7f812a9200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:10:39 ar_logcat: ^[[35;22m[124454254][0300][0x7f812a9200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:10:39 ar_logcat: 374d7000 37911000 37a21000 0 0 0 37b2b000 37f65000 38075000 0 0 0 
Jan 25 00:10:39 ar_logcat: ^[[35;22m[124454256][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:10:40 ar_logcat: ^[[35;22m[124454258][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:10:40 ar_logcat: ^[[35;22m[124454270][0300][0x7f80717200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:10:40 ar_logcat: ^[[35;22m[124454270][0300][0x7f80717200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:10:40 ar_logcat: ^[[31;22m[124454272][0498][0x7ef28781a0][VPSS_MPP][ERROR] mpi_vpss.c: ar_hal_vpss_create : 870 bind_register_sender failed 0x80028003^[[0m
Jan 25 00:10:40 ar_logcat: ^[[33;22m[124454273][0300][0x7f81f5f200][VO_CORE][WARN] display_api.c: dc_init : 2258 use dc_reg_irq_by_cmd!^[[0m
Jan 25 00:10:40 ar_logcat: ^[[31;22m[124454273][0300][0x7f81f5f200][VO_CORE][ERROR] display_top.c: ar_display_dev_enable : 3521 not support set irq_type:00][0x7f80738200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[
Jan 25 00:10:40 ar_logcat: ^[[35;22m[124454274][0300][0x7f80738200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:10:40 ar_logcat: ^[[35;22m[124454274][0300][0x7f8081b200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:10:40 ar_logcat: ^[[35;22m[124454274][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:10:40 ar_logcat: ^[[35;22m[124454274][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:10:40 ar_logcat: ^[[35;22m[124454274][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:10:40 ar_logcat: 36e7d000 372b7000 373c7000 0 0 0 374d1000 3790b000 37a1b000 0 0 0 
Jan 25 00:10:40 ar_logcat: aec index 198685398 line 1125 gain 1.000000 
Jan 25 00:10:40 ar_logcat: ^[[35;22m[124454274][0300][0x7f8081b200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:10:40 ar_logcat: ^[[35;22m[124454274][0300][0x7f8081b200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:10:40 ar_logcat: 374d7000 37911000 37a21000 0 0 0 37b2b000 37f65000 38075000 0 0 0 
Jan 25 00:10:40 ar_logcat: ^[[35;22m[124454276][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:10:40 ar_logcat: ^[[35;22m[124454278][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:10:41 ar_logcat: ^[[33;22m[124454427][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:41 ar_logcat: ^[[0m
Jan 25 00:10:42 ar_logcat: ^[[33;22m[124454467][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:42 ar_logcat: ^[[0m
Jan 25 00:10:43 ar_logcat: ^[[33;22m[124454601][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:43 ar_logcat: ^[[0m
Jan 25 00:10:43 ar_logcat: ^[[33;22m[124454627][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:43 ar_logcat: ^[[0m
Jan 25 00:10:44 ar_logcat: ^[[33;22m[124454681][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:44 ar_logcat: ^[[0m
Jan 25 00:10:44 ar_logcat: ^[[33;22m[124454694][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:44 ar_logcat: ^[[0m
Jan 25 00:10:44 ar_logcat: ^[[33;22m[124454734][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:44 ar_logcat: ^[[0m
Jan 25 00:10:45 ar_logcat: ^[[33;22m[124454761][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:45 ar_logcat: ^[[0m
Jan 25 00:10:45 ar_logcat: ^[[33;22m[124454800][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:45 ar_logcat: ^[[0m
Jan 25 00:10:45 ar_logcat: ^[[33;22m[124454854][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:45 ar_logcat: ^[[0m
Jan 25 00:10:46 ar_logcat: ^[[33;22m[124454954][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:46 ar_logcat: ^[[0m
Jan 25 00:10:47 ar_logcat: ^[[33;22m[124454987][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:47 ar_logcat: ^[[0m
Jan 25 00:10:48 ar_logcat: ^[[33;22m[124455064][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:48 ar_logcat: ^[[0m
Jan 25 00:10:48 ar_logcat: ^[[33;22m[124455144][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:48 ar_logcat: ^[[0m
Jan 25 00:10:48 ar_logcat: ^[[33;22m[124455157][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:48 ar_logcat: ^[[0m
Jan 25 00:10:49 ar_logcat: ^[[33;22m[124455181][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:49 ar_logcat: ^[[0m
Jan 25 00:10:49 ar_logcat: ^[[33;22m[124455184][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:49 ar_logcat: ^[[0m
Jan 25 00:10:49 ar_logcat: ^[[33;22m[124455221][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:49 ar_logcat: ^[[0m
Jan 25 00:10:52 ar_logcat: ^[[33;22m[124455474][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:52 ar_logcat: ^[[0m
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                