[    0.459817] sw reset
[    0.460762] sw reset
[    0.462082] aw35615 device ID: 0x91
[    0.463243] pd := off
[    0.463244] vbus is already Off
[    0.463246] charge is already Off
[    0.463247] vconn is already Off
[    0.463467] pd header := Sink, Device
[    0.463478] cc1=Open, cc2=Open
[    0.464672] pd := off
[    0.464674] vbus is already Off
[    0.464676] charge is already Off
[    0.464677] vconn is already Off
[    0.464918] pd header := Sink, Device
[    0.464926] cc := Rd
[    0.467510] start drp toggling
[    0.468227] IRQ: 0x80, a: 0x00, b: 0x00, status0: 0x83, status1: 0x28
[    0.468230] IRQ: VBUS_OK, vbus=On
[    0.468239] gpio_intn_value:0
[    0.468352] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.468909] IRQ: 0x80, a: 0x00, b: 0x00, status0: 0x83, status1: 0x28
[    0.468911] IRQ: VBUS_OK, vbus=On
[    0.468913] gpio_intn_value:1
[    0.469025] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.472000] IRQ: 0x00, a: 0x40, b: 0x00, status0: 0x83, status1: 0x28
[    0.472002] IRQ: TOGDONE
[    0.473474] detected cc1=Rp-3.0, cc2=Open
[    0.473476] gpio_intn_value:1
[    0.473587] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.473591] cc1=Rp-3.0, cc2=Open
[    0.558786] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc1, status1: 0x08
[    0.558791] IRQ: BC_LVL, handler pending
[    0.558801] gpio_intn_value:0
[    0.558915] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.559488] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0x93, status1: 0x08
[    0.559491] IRQ: BC_LVL, handler pending
[    0.559493] gpio_intn_value:1
[    0.559608] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.560384] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc1, status1: 0x08
[    0.560389] IRQ: BC_LVL, handler pending
[    0.560392] gpio_intn_value:0
[    0.560504] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.561055] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0x93, status1: 0x08
[    0.561057] IRQ: BC_LVL, handler pending
[    0.561059] gpio_intn_value:1
[    0.561172] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.561970] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc1, status1: 0x08
[    0.561972] IRQ: BC_LVL, handler pending
[    0.561974] gpio_intn_value:0
[    0.562085] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.562666] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0x93, status1: 0x08
[    0.562669] IRQ: BC_LVL, handler pending
[    0.562671] gpio_intn_value:1
[    0.562787] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.594337] BC_LVL handler, status0=0x93
[    0.666133] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc1, status1: 0x08
[    0.666144] IRQ: BC_LVL, handler pending
[    0.666152] gpio_intn_value:0
[    0.666263] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.666821] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0x93, status1: 0x08
[    0.666824] IRQ: BC_LVL, handler pending
[    0.666826] gpio_intn_value:1
[    0.666938] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.667740] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc1, status1: 0x08
[    0.667744] IRQ: BC_LVL, handler pending
[    0.667746] gpio_intn_value:0
[    0.667857] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.668416] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0x93, status1: 0x08
[    0.668419] IRQ: BC_LVL, handler pending
[    0.668422] gpio_intn_value:1
[    0.668536] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.669439] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc1, status1: 0x08
[    0.669448] IRQ: BC_LVL, handler pending
[    0.669455] gpio_intn_value:0
[    0.669566] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.670118] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0x93, status1: 0x08
[    0.670120] IRQ: BC_LVL, handler pending
[    0.670122] gpio_intn_value:1
[    0.670233] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.673938] pd header := Sink, Device
[    0.673992] vbus is already Off
[    0.675266] pd := on
[    0.704335] BC_LVL handler, status0=0x93
[    0.773631] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd2, status1: 0x08
[    0.773638] IRQ: BC_LVL, handler pending
[    0.773648] gpio_intn_value:0
[    0.773764] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.774330] IRQ: 0x51, a: 0x00, b: 0x01, status0: 0x93, status1: 0x08
[    0.774337] IRQ: BC_LVL, handler pending
[    0.774342] IRQ: PD sent good CRC
[    0.774953] PD message header: 17a1 len:4 crc:71d64ee3
[    0.774966] gpio_intn_value:0
[    0.775152] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.775780] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0x93, status1: 0x28
[    0.775785] IRQ: BC_LVL, handler pending
[    0.775794] gpio_intn_value:1
[    0.775907] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.776535] sending PD message header: 1042
[    0.776538] sending PD message len: 4
[    0.777152] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd2, status1: 0x20
[    0.777155] IRQ: BC_LVL, handler pending
[    0.777163] gpio_intn_value:0
[    0.777275] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.777828] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc1, status1: 0x08
[    0.777830] IRQ: BC_LVL, handler pending
[    0.777831] gpio_intn_value:0
[    0.777943] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.778494] IRQ: 0x51, a: 0x04, b: 0x00, status0: 0x93, status1: 0x08
[    0.778496] IRQ: BC_LVL, handler pending
[    0.778498] IRQ: PD tx success
[    0.778927] PD message header: 1a1 len:0 crc:81c2afc1
[    0.778942] gpio_intn_value:1
[    0.779055] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.780493] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0x93, status1: 0x08
[    0.780501] IRQ: BC_LVL, handler pending
[    0.780509] gpio_intn_value:0
[    0.780621] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.781177] IRQ: 0x51, a: 0x00, b: 0x01, status0: 0x93, status1: 0x08
[    0.781179] IRQ: BC_LVL, handler pending
[    0.781181] IRQ: PD sent good CRC
[    0.781609] PD message header: 963 len:0 crc:76d5923f
[    0.781615] gpio_intn_value:0
[    0.781744] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.782300] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0x93, status1: 0x28
[    0.782304] IRQ: BC_LVL, handler pending
[    0.782307] gpio_intn_value:1
[    0.782425] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.809038] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0x93, status1: 0x08
[    0.809051] IRQ: BC_LVL, handler pending
[    0.809060] gpio_intn_value:0
[    0.809172] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.809724] IRQ: 0x51, a: 0x00, b: 0x01, status0: 0x93, status1: 0x08
[    0.809726] IRQ: BC_LVL, handler pending
[    0.809727] IRQ: PD sent good CRC
[    0.810155] PD message header: b66 len:0 crc:e5ac0756
[    0.810164] gpio_intn_value:0
[    0.810313] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.810915] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0x93, status1: 0x28
[    0.810920] IRQ: BC_LVL, handler pending
[    0.810930] gpio_intn_value:1
[    0.811045] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.854313] BC_LVL handler, status0=0x93
[    0.861244] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc1, status1: 0x08
[    0.861248] IRQ: BC_LVL, handler pending
[    0.861255] gpio_intn_value:0
[    0.861367] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.861921] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0xd0, status1: 0x08
[    0.861923] IRQ: BC_LVL, handler pending
[    0.861925] gpio_intn_value:0
[    0.862036] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.862587] IRQ: 0x41, a: 0x00, b: 0x01, status0: 0x93, status1: 0x08
[    0.862589] IRQ: BC_LVL, handler pending
[    0.862590] IRQ: PD sent good CRC
[    0.863200] PD message header: 1d6f len:4 crc:a49bdc77
[    0.863207] gpio_intn_value:1
[    0.863319] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.864317] sending PD message header: 524f
[    0.864318] sending PD message len: 20
[    0.864955] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd1, status1: 0x20
[    0.864957] IRQ: BC_LVL, handler pending
[    0.864959] gpio_intn_value:0
[    0.865070] aw35615_i2c_read AW_REG_STATUS1 :0x20
[    0.865622] IRQ: 0x01, a: 0x00, b: 0x00, status0: 0xd2, status1: 0x28
[    0.865624] IRQ: BC_LVL, handler pending
[    0.865627] gpio_intn_value:0
[    0.865738] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.866290] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0x93, status1: 0x08
[    0.866292] IRQ: BC_LVL, handler pending
[    0.866293] gpio_intn_value:0
[    0.866404] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.866956] IRQ: 0x51, a: 0x04, b: 0x00, status0: 0x93, status1: 0x08
[    0.866958] IRQ: BC_LVL, handler pending
[    0.866959] IRQ: PD tx success
[    0.867386] PD message header: 361 len:0 crc:a43619a3
[    0.867391] gpio_intn_value:1
[    0.867502] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.868380] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc2, status1: 0x08
[    0.868382] IRQ: BC_LVL, handler pending
[    0.868384] gpio_intn_value:0
[    0.868495] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.869046] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0xd2, status1: 0x08
[    0.869048] IRQ: BC_LVL, handler pending
[    0.869049] gpio_intn_value:0
[    0.869160] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.869712] IRQ: 0x41, a: 0x00, b: 0x01, status0: 0x93, status1: 0x08
[    0.869714] IRQ: BC_LVL, handler pending
[    0.869715] IRQ: PD sent good CRC
[    0.870370] PD message header: 1f6f len:4 crc:2fa78255
[    0.870379] gpio_intn_value:1
[    0.870508] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.871320] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc1, status1: 0x08
[    0.871325] IRQ: BC_LVL, handler pending
[    0.871332] gpio_intn_value:0
[    0.871454] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.872110] IRQ: 0x51, a: 0x00, b: 0x01, status0: 0x93, status1: 0x08
[    0.872118] IRQ: BC_LVL, handler pending
[    0.872123] IRQ: PD sent good CRC
[    0.872831] PD message header: 116f len:4 crc:73de9e98
[    0.872850] gpio_intn_value:0
[    0.872965] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.873538] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0x93, status1: 0x28
[    0.873543] IRQ: BC_LVL, handler pending
[    0.873552] gpio_intn_value:1
[    0.873695] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.874446] sending PD message header: 244f
[    0.874451] sending PD message len: 8
[    0.875117] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd2, status1: 0x20
[    0.875128] IRQ: BC_LVL, handler pending
[    0.875139] gpio_intn_value:0
[    0.875251] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.875810] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc0, status1: 0x08
[    0.875815] IRQ: BC_LVL, handler pending
[    0.875823] gpio_intn_value:0
[    0.875934] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.876495] IRQ: 0x51, a: 0x04, b: 0x00, status0: 0x93, status1: 0x08
[    0.876501] IRQ: BC_LVL, handler pending
[    0.876504] IRQ: PD tx success
[    0.876936] PD message header: 561 len:0 crc:4d55bc96
[    0.876950] gpio_intn_value:1
[    0.877065] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.878314] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc1, status1: 0x08
[    0.878319] IRQ: BC_LVL, handler pending
[    0.878323] gpio_intn_value:0
[    0.878442] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.879021] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0xd1, status1: 0x08
[    0.879026] IRQ: BC_LVL, handler pending
[    0.879035] gpio_intn_value:0
[    0.879159] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.879776] IRQ: 0x41, a: 0x00, b: 0x01, status0: 0x93, status1: 0x08
[    0.879781] IRQ: BC_LVL, handler pending
[    0.879787] IRQ: PD sent good CRC
[    0.880400] PD message header: 136f len:4 crc:a8b99bdc
[    0.880414] gpio_intn_value:1
[    0.880528] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.881283] sending PD message header: 264f
[    0.881289] sending PD message len: 8
[    0.881990] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd2, status1: 0x20
[    0.881998] IRQ: BC_LVL, handler pending
[    0.882010] gpio_intn_value:0
[    0.882123] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.882725] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0x93, status1: 0x08
[    0.882730] IRQ: BC_LVL, handler pending
[    0.882739] gpio_intn_value:0
[    0.882851] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.883460] IRQ: 0x51, a: 0x04, b: 0x00, status0: 0x93, status1: 0x08
[    0.883471] IRQ: BC_LVL, handler pending
[    0.883476] IRQ: PD tx success
[    0.883944] PD message header: 761 len:0 crc:a35bddba
[    0.883963] gpio_intn_value:1
[    0.884078] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.885143] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc1, status1: 0x08
[    0.885153] IRQ: BC_LVL, handler pending
[    0.885162] gpio_intn_value:0
[    0.885275] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.885859] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0xd2, status1: 0x08
[    0.885862] IRQ: BC_LVL, handler pending
[    0.885870] gpio_intn_value:0
[    0.885981] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.886592] IRQ: 0x41, a: 0x00, b: 0x01, status0: 0x93, status1: 0x08
[    0.886611] IRQ: BC_LVL, handler pending
[    0.886617] IRQ: PD sent good CRC
[    0.887278] PD message header: 156f len:4 crc:bbec3cf2
[    0.887317] gpio_intn_value:1
[    0.887448] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.888675] sending PD message header: 184f
[    0.888685] sending PD message len: 4
[    0.889379] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd1, status1: 0x28
[    0.889391] IRQ: BC_LVL, handler pending
[    0.889401] gpio_intn_value:0
[    0.889516] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.890072] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0x93, status1: 0x08
[    0.890076] IRQ: BC_LVL, handler pending
[    0.890080] gpio_intn_value:0
[    0.890192] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.890745] IRQ: 0x51, a: 0x04, b: 0x00, status0: 0x93, status1: 0x08
[    0.890748] IRQ: BC_LVL, handler pending
[    0.890751] IRQ: PD tx success
[    0.891184] PD message header: 961 len:0 crc:44e3f0bd
[    0.891199] gpio_intn_value:1
[    0.891319] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.892438] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc2, status1: 0x08
[    0.892455] IRQ: BC_LVL, handler pending
[    0.892470] gpio_intn_value:0
[    0.892592] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.893198] IRQ: 0x51, a: 0x00, b: 0x01, status0: 0x93, status1: 0x08
[    0.893218] IRQ: BC_LVL, handler pending
[    0.893227] IRQ: PD sent good CRC
[    0.894008] PD message header: 276f len:8 crc:cea87bc0
[    0.894045] gpio_intn_value:0
[    0.894171] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.894849] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0x93, status1: 0x28
[    0.894863] IRQ: BC_LVL, handler pending
[    0.894875] gpio_intn_value:1
[    0.894995] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.895768] sending PD message header: 2a4f
[    0.895778] sending PD message len: 8
[    0.896569] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd1, status1: 0x28
[    0.896585] IRQ: BC_LVL, handler pending
[    0.896598] gpio_intn_value:0
[    0.896757] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.897439] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0x93, status1: 0x08
[    0.897450] IRQ: BC_LVL, handler pending
[    0.897461] gpio_intn_value:0
[    0.897575] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.898182] IRQ: 0x51, a: 0x04, b: 0x00, status0: 0x93, status1: 0x08
[    0.898189] IRQ: BC_LVL, handler pending
[    0.898193] IRQ: PD tx success
[    0.898625] PD message header: b61 len:0 crc:aaed9191
[    0.898640] gpio_intn_value:1
[    0.898754] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.899534] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc1, status1: 0x08
[    0.899544] IRQ: BC_LVL, handler pending
[    0.899554] gpio_intn_value:0
[    0.899669] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.900256] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0xd2, status1: 0x08
[    0.900265] IRQ: BC_LVL, handler pending
[    0.900275] gpio_intn_value:0
[    0.900399] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.901022] IRQ: 0x41, a: 0x00, b: 0x01, status0: 0x93, status1: 0x08
[    0.901029] IRQ: BC_LVL, handler pending
[    0.901032] IRQ: PD sent good CRC
[    0.901740] PD message header: 296f len:8 crc:501e30a9
[    0.901752] gpio_intn_value:1
[    0.901864] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.902611] sending PD message header: 1c4f
[    0.902613] sending PD message len: 4
[    0.903255] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd1, status1: 0x20
[    0.903258] IRQ: BC_LVL, handler pending
[    0.903263] gpio_intn_value:0
[    0.903374] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.903927] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd3, status1: 0x08
[    0.903930] IRQ: BC_LVL, handler pending
[    0.903931] gpio_intn_value:0
[    0.904042] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.904598] IRQ: 0x51, a: 0x04, b: 0x00, status0: 0x93, status1: 0x08
[    0.904600] IRQ: BC_LVL, handler pending
[    0.904602] IRQ: PD tx success
[    0.905030] PD message header: d61 len:0 crc:438e34a4
[    0.905039] gpio_intn_value:1
[    0.905156] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.925183] sending PD message header: 2e4f
[    0.925188] sending PD message len: 8
[    0.925901] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd1, status1: 0x20
[    0.925907] IRQ: BC_LVL, handler pending
[    0.925915] gpio_intn_value:0
[    0.926031] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.926607] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc1, status1: 0x08
[    0.926612] IRQ: BC_LVL, handler pending
[    0.926623] gpio_intn_value:0
[    0.926736] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.927318] IRQ: 0x51, a: 0x04, b: 0x00, status0: 0x93, status1: 0x08
[    0.927327] IRQ: BC_LVL, handler pending
[    0.927333] IRQ: PD tx success
[    0.927801] PD message header: f61 len:0 crc:ad805588
[    0.927818] gpio_intn_value:1
[    0.927931] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.964477] BC_LVL handler, status0=0x93
