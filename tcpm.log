[    0.462086] Setting usb_comm capable false
[    0.463249] Setting voltage/current limit 0 mV 0 mA
[    0.463252] polarity 0
[    0.463254] Requesting mux state 0, usb-role 0, orientation 0
[    0.463471] state change INVALID_STATE -> SNK_UNATTACHED [rev1 NONE_AMS]
[    0.463480] CC1: 0 -> 0, CC2: 0 -> 0 [state SNK_UNATTACHED, polarity 0, disconnected]
[    0.463484] 2-0022: registered
[    0.463486] Setting usb_comm capable false
[    0.464679] Setting voltage/current limit 0 mV 0 mA
[    0.464690] polarity 0
[    0.464692] Requesting mux state 0, usb-role 0, orientation 0
[    0.464924] cc:=2
[    0.466313] pending state change PORT_RESET -> PORT_RESET_WAIT_OFF @ 100 ms [rev1 NONE_AMS]
[    0.466321] state change PORT_RESET -> PORT_RESET_WAIT_OFF [delayed 100 ms]
[    0.466325] state change PORT_RESET_WAIT_OFF -> SNK_UNATTACHED [rev1 NONE_AMS]
[    0.466332] Start toggling
[    0.469030] VBUS on
[    0.473594] CC1: 0 -> 5, CC2: 0 -> 0 [state TOGGLING, polarity 0, connected]
[    0.473601] state change TOGGLING -> SNK_ATTACH_WAIT [rev1 NONE_AMS]
[    0.473605] pending state change SNK_ATTACH_WAIT -> SNK_DEBOUNCED @ 200 ms [rev1 NONE_AMS]
[    0.673626] state change SNK_ATTACH_WAIT -> SNK_DEBOUNCED [delayed 200 ms]
[    0.673636] state change SNK_DEBOUNCED -> SNK_ATTACHED [rev1 NONE_AMS]
[    0.673639] polarity 0
[    0.673642] Requesting mux state 1, usb-role 2, orientation 1
[    0.673945] state change SNK_ATTACHED -> SNK_STARTUP [rev1 NONE_AMS]
[    0.673977] state change SNK_STARTUP -> SNK_DISCOVERY [rev2 NONE_AMS]
[    0.673981] Setting voltage/current limit 5000 mV 3000 mA
[    0.673990] vbus=0 charge:=1
[    0.673995] state change SNK_DISCOVERY -> SNK_WAIT_CAPABILITIES [rev2 NONE_AMS]
[    0.675274] pending state change SNK_WAIT_CAPABILITIES -> HARD_RESET_SEND @ 620 ms [rev2 NONE_AMS]
[    0.774969] PD RX, header: 0x17a1 [1], 1 objects
[    0.774976] PD RX, object:0x3601912c
[    0.774982]  PDO 0: type 0, 5000 mV, 3000 mA [RSHUD]
[    0.775133] state change SNK_WAIT_CAPABILITIES -> SNK_NEGOTIATE_CAPABILITIES [rev2 POWER_NEGOTIATION]
[    0.775142] Setting usb_comm capable true
[    0.775156] cc=2 cc1=5 cc2=0 vbus=0 vconn=sink polarity=0
[    0.775161] Requesting PDO 0: 5000 mV, 3000 mA
[    0.775164] PD TX, header: 0x1042
[    0.778931] PD TX complete, status: 0
[    0.778945] pending state change SNK_NEGOTIATE_CAPABILITIES -> HARD_RESET_SEND @ 60 ms [rev2 POWER_NEGOTIATION]
[    0.781644] PD RX, header: 0x963 [1], 0 objects
[    0.781650] state change SNK_NEGOTIATE_CAPABILITIES -> SNK_TRANSITION_SINK [rev2 POWER_NEGOTIATION]
[    0.781659] pending state change SNK_TRANSITION_SINK -> HARD_RESET_SEND @ 500 ms [rev2 POWER_NEGOTIATION]
[    0.810180] PD RX, header: 0xb66 [1], 0 objects
[    0.810185] Setting voltage/current limit 5000 mV 3000 mA
[    0.810200] state change SNK_TRANSITION_SINK -> SNK_READY [rev2 POWER_NEGOTIATION]
[    0.810353] AMS POWER_NEGOTIATION finished
[    0.863215] PD RX, header: 0x1d6f [1], 1 objects
[    0.863218] PD RX, object:0xff008001
[    0.863223] Rx VDM cmd 0xff008001 type 0 cmd 1 len 1 adev           (null)
[    0.863234] tcpm_queue_vdm
[    0.863236] vdm_run_state_machine vdm_state:1
[    0.863239] AMS DISCOVER_IDENTITY start
[    0.863241] vdm_run_state_machine vdm_state:4
[    0.863243] PD TX, header: 0x524f
[    0.867388] PD TX complete, status: 0
[    0.867393] AMS DISCOVER_IDENTITY finished
[    0.867397] vdm_run_state_machine vdm_state:2
[    0.867399] vdm_run_state_machine vdm_state:-1
[    0.870380] PD RX, header: 0x1f6f [1], 1 objects
[    0.870386] PD RX, object:0x33180001
[    0.870390] usvdm playload:33180001
[    0.872851] PD RX, header: 0x116f [1], 1 objects
[    0.872856] PD RX, object:0xff008002
[    0.872863] Rx VDM cmd 0xff008002 type 0 cmd 2 len 1 adev           (null)
[    0.872872] svid 0xff01
[    0.872874] tcpm_queue_vdm
[    0.872878] vdm_run_state_machine vdm_state:1
[    0.872881] AMS DISCOVER_SVIDS start
[    0.872884] vdm_run_state_machine vdm_state:4
[    0.872885] PD TX, header: 0x244f
[    0.876940] PD TX complete, status: 0
[    0.876960] AMS DISCOVER_SVIDS finished
[    0.876967] vdm_run_state_machine vdm_state:2
[    0.876968] vdm_run_state_machine vdm_state:-1
[    0.880425] PD RX, header: 0x136f [1], 1 objects
[    0.880428] PD RX, object:0xff018003
[    0.880432] Rx VDM cmd 0xff018003 type 0 cmd 3 len 1 adev           (null)
[    0.880442] SRC SVID 1: 0xff01
[    0.880445] tcpm_queue_vdm
[    0.880449] vdm_run_state_machine vdm_state:1
[    0.880452] AMS DISCOVER_MODES start
[    0.880454] vdm_run_state_machine vdm_state:4
[    0.880456] PD TX, header: 0x264f
[    0.883951] PD TX complete, status: 0
[    0.883963] AMS DISCOVER_MODES finished
[    0.883973] vdm_run_state_machine vdm_state:2
[    0.883974] vdm_run_state_machine vdm_state:-1
[    0.887319] PD RX, header: 0x156f [1], 1 objects
[    0.887324] PD RX, object:0xff018104
[    0.887335] Rx VDM cmd 0xff018104 type 0 cmd 4 len 1 adev ffffffc02f4e5008
[    0.887354]  Alternate mode 0: SVID 0xff01, VDO 1: 0x00000405
[    0.887900] tcpm_queue_vdm
[    0.887916] vdm_run_state_machine vdm_state:1
[    0.887927] AMS DFP_TO_UFP_ENTER_MODE start
[    0.887931] vdm_run_state_machine vdm_state:4
[    0.887932] PD TX, header: 0x184f
[    0.891189] PD TX complete, status: 0
[    0.891219] AMS DFP_TO_UFP_ENTER_MODE finished
[    0.891231] vdm_run_state_machine vdm_state:2
[    0.891233] vdm_run_state_machine vdm_state:-1
[    0.894051] PD RX, header: 0x276f [1], 2 objects
[    0.894059] PD RX, object:0xff018110
[    0.894060] PD RX, object:0x1
[    0.894066] Rx VDM cmd 0xff018110 type 0 cmd 16 len 2 adev ffffffc02f4e5008
[    0.894132] tcpm_queue_vdm
[    0.894139] vdm_run_state_machine vdm_state:1
[    0.894144] AMS STRUCTURED_VDMS start
[    0.894147] vdm_run_state_machine vdm_state:4
[    0.894149] PD TX, header: 0x2a4f
[    0.898630] PD TX complete, status: 0
[    0.898641] AMS STRUCTURED_VDMS finished
[    0.898650] vdm_run_state_machine vdm_state:2
[    0.898652] vdm_run_state_machine vdm_state:-1
[    0.901754] PD RX, header: 0x296f [1], 2 objects
[    0.901760] PD RX, object:0xff018111
[    0.901762] PD RX, object:0x406
[    0.901766] Rx VDM cmd 0xff018111 type 0 cmd 17 len 2 adev ffffffc02f4e5008
[    0.901841] tcpm_queue_vdm
[    0.901903] vdm_run_state_machine vdm_state:1
[    0.901906] AMS STRUCTURED_VDMS start
[    0.901910] vdm_run_state_machine vdm_state:4
[    0.901912] PD TX, header: 0x1c4f
[    0.905033] PD TX complete, status: 0
[    0.905042] AMS STRUCTURED_VDMS finished
[    0.905047] vdm_run_state_machine vdm_state:2
[    0.905052] vdm_run_state_machine vdm_state:-1
[    0.924302] tcpm_queue_vdm
[    0.924371] vdm_run_state_machine vdm_state:1
[    0.924376] AMS ATTENTION start
[    0.924379] vdm_run_state_machine vdm_state:4
[    0.924381] PD TX, header: 0x2e4f
[    0.927807] PD TX complete, status: 0
[    0.927862] AMS ATTENTION finished
[    0.958015] vdm_run_state_machine vdm_state:2
[    0.958027] vdm_run_state_machine vdm_state:-1
