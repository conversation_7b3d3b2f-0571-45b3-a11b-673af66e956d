Jan 25 00:00:00 rcS: kernel start finish
Jan 25 00:00:00 rcS: Mpp service initpre
Jan 25 00:00:00 rcS: mount: mounting pstore on /sys/fs/pstore failed: No such file or directory
Jan 25 00:00:00 rcS: ls: /sys/fs/pstore: No such file or directory
Jan 25 00:00:00 rcS: insmod ar_osal.ko
Jan 25 00:00:00 rcS: insmod camera camera_pwr_ioctl.ko
Jan 25 00:00:00 rcS: insmod camera camera_timestamp.ko
Jan 25 00:00:00 rcS: insmod camera camera_plug_detect.ko
Jan 25 00:00:00 rcS: insmod ar_pack.ko
Jan 25 00:00:00 runsvdir: Xreal core init.
Jan 25 00:00:00 runsvdir: Run Mpp service.
Jan 25 00:00:00 runsvdir: /factory/start.conf not exist, please add it with your own config as following:
Jan 25 00:00:00 runsvdir: kern_log_level	1
Jan 25 00:00:00 runsvdir: nw_itf_name		eth0
Jan 25 00:00:00 runsvdir: nw_itf_mac		aa:bb:cc:dd:ee:ff
Jan 25 00:00:00 runsvdir: Run Mpp_vb_config service.
Jan 25 00:00:00 runsvdir: Current boot count is 1371
Jan 25 00:00:00 runsvdir: ./run: line 38: can't create /proc/irq/223/smp_affinity: nonexistent directory
Jan 25 00:00:00 runsvdir: Run Dropbear service.
Jan 25 00:00:00 runsvdir: net.core.wmem_default = 6000000
Jan 25 00:00:00 runsvdir: net.core.wmem_max = 6000000
Jan 25 00:00:00 runsvdir: net.ipv4.tcp_wmem = 40960 6000000 7254080
Jan 25 00:00:00 runsvdir: net.ipv4.tcp_rmem = 40960 6000000 7254080
Jan 25 00:00:00 runsvdir: vm.min_free_kbytes = 5000
Jan 25 00:00:00 runsvdir: kernel.core_pattern = /usrdata/log/current_log_dir/core.%e.%p
Jan 25 00:00:00 runsvdir: kernel.softlockup_panic = 1
Jan 25 00:00:00 runsvdir: kernel.softlockup_all_cpu_backtrace = 1
Jan 25 00:00:00 runsvdir: start parse factory json file!
Jan 25 00:00:00 runsvdir: Run Command service.
Jan 25 00:00:00 runsvdir: hw_version:GF_6
Jan 25 00:00:00 runsvdir: SoC revision: 0
Jan 25 00:00:00 runsvdir: ================================
Jan 25 00:00:00 runsvdir: SoC ID: 21317
Jan 25 00:00:00 runsvdir: ================================
Jan 25 00:00:00 runsvdir: SoC chip ID: 2612742289705979130
Jan 25 00:00:00 runsvdir: ================================
Jan 25 00:00:00 runsvdir: SoC information:
Jan 25 00:00:00 runsvdir: Revision ID: 0
Jan 25 00:00:00 runsvdir: SoC suffix: 
Jan 25 00:00:00 runsvdir: SoC product ID: 21317
Jan 25 00:00:00 runsvdir: Soc PD year: 36
Jan 25 00:00:00 runsvdir: Soc PD week: 66
Jan 25 00:00:00 runsvdir: Soc ID info: 00956A6M
Jan 25 00:00:00 runsvdir: Soc SN: 59821306
Jan 25 00:00:00 runsvdir: ================================
Jan 25 00:00:00 runsvdir: run ...
Jan 25 00:00:00 runsvdir: remove /usrdata/log/log_66
Jan 25 00:00:00 runsvdir: [apps_prepare] [20270125 000000.389] [main.c] [main-73] : pilot zip exist
Jan 25 00:00:00 runsvdir: [apps_prepare] [20270125 000000.433] [version.c] [read_pilot_zip_version-129] : pilot zip version [1.7.0.20250905173354]
Jan 25 00:00:00 runsvdir: [apps_prepare] [20270125 000000.433] [version.c] [read_pilot_version-67] : version file : /usrdata/pilot/pilot_version.txt
Jan 25 00:00:00 runsvdir: [apps_prepare] [20270125 000000.437] [version.c] [read_pilot_version-85] : pilot [/usrdata/pilot/pilot_version.txt] version [1.7.0.20250905173354]
Jan 25 00:00:00 runsvdir: [apps_prepare] [20270125 000000.437] [main.c] [main-101] : pilot in glass is equal to or greater than it in the file, skip copy
Jan 25 00:00:00 runsvdir: limit offline log number to 5.
Jan 25 00:00:00 runsvdir: iManufacturer:XREAL
Jan 25 00:00:00 runsvdir: iProduct:XREAL One
Jan 25 00:00:00 runsvdir: VID:0x3318
Jan 25 00:00:00 runsvdir: PID (Kernel):0x0438
Jan 25 00:00:00 runsvdir: [command_service] [20270125 000000.469] [upgrade/upgrade.c] [init_upgrade_function-12] : media type : emmc
Jan 25 00:00:00 runsvdir: [command_service] [20270125 000000.470] [upgrade/upgrade.c] [init_upgrade_function-13] : adjust emmc : true
Jan 25 00:00:00 runsvdir: [command_service] [20270125 000000.470] [upgrade/upgrade.c] [init_upgrade_function-14] : adjust nand : false
Jan 25 00:00:00 runsvdir: [command_service] [20270125 000000.470] [upgrade/upgrade.c] [init_upgrade_function-16] : emmc init
Jan 25 00:00:00 runsvdir: [command_service] [20270125 000000.472] [upgrade/emmc_upgrade.c] [get_mmc_start_part-489] : start from gpt : 0
Jan 25 00:00:00 runsvdir: [command_service] [20270125 000000.472] [upgrade/emmc_upgrade.c] [get_mmc_param_start_part-522] : root number : 19
Jan 25 00:00:00 runsvdir: [command_service] [20270125 000000.474] [command_service.c] [check_ipc_file-428] : file exist
Jan 25 00:00:00 runsvdir: Run Servicemanager service.
Jan 25 00:00:00 runsvdir: Run Audio service.
Jan 25 00:00:00 runsvdir: argc is error!!!
Jan 25 00:00:00 runsvdir: [usb_gadget_client] ERROR nr_usb_gadget.cpp getService:79: Retrying to get UsbGadgetService
Jan 25 00:00:00 runsvdir: [usb_gadget_client] ERROR nr_usb_gadget.cpp getService:79: Retrying to get UsbGadgetService
Jan 25 00:00:00 runsvdir: bootchartd is not running.
Jan 25 00:00:00 runsvdir: csn:H451X40483
Jan 25 00:00:00 runsvdir: usn:A1#H1X652H178615M
Jan 25 00:00:00 runsvdir: psn:XS4513010LMB1AM42H
Jan 25 00:00:00 runsvdir: Run Pilot service.
Jan 25 00:00:00 runsvdir: [usb_gadget_client] ERROR nr_usb_gadget.cpp getService:79: Retrying to get UsbGadgetService
Jan 25 00:00:00 runsvdir: Run Ar watchdog service.
Jan 25 00:00:00 runsvdir: Property sys.init.boot_complete set to 1
Jan 25 00:00:00 runsvdir: Core init complete.
Jan 25 00:00:00 runsvdir: Run power_manager service.
Jan 25 00:00:00 runsvdir: Property ro.bsp.usb_vid set to 0x3318
Jan 25 00:00:00 runsvdir: [usb_gadget_client] ERROR nr_usb_gadget.cpp getService:79: Retrying to get UsbGadgetService
Jan 25 00:00:00 runsvdir: Property reached valid value: true
Jan 25 00:00:00 runsvdir: Wait app_prepare done success.
Jan 25 00:00:00 runsvdir: Initcore service exit, recycling resource.
Jan 25 00:00:00 runsvdir: Property ro.bsp.usb_pid set to 0x0438
Jan 25 00:00:00 runsvdir: ar_clk exist, start to config sys clk
Jan 25 00:00:00 runsvdir: [usb_gadget_client] ERROR nr_usb_gadget.cpp getService:79: Retrying to get UsbGadgetService
Jan 25 00:00:00 runsvdir: Property ro.bsp.product_name set to XREAL One
Jan 25 00:00:00 runsvdir: disable clock 27
Jan 25 00:00:00 runsvdir: [usb_gadget_client] ERROR nr_usb_gadget.cpp getService:79: Retrying to get UsbGadgetService
Jan 25 00:00:00 runsvdir: open /dev/watchdog0 ret = 3 
Jan 25 00:00:00 runsvdir: disable clock 31
Jan 25 00:00:00 runsvdir: Property ro.bsp.product_code set to GF
Jan 25 00:00:00 runsvdir: Property ro.bsp.glasses_id set to H451X40483
Jan 25 00:00:00 runsvdir: [usb_gadget_client] ERROR nr_usb_gadget.cpp getService:79: Retrying to get UsbGadgetService
Jan 25 00:00:00 runsvdir: set clock 1, freq 300000000
Jan 25 00:00:00 runsvdir: Property ro.bsp.glasses_sn0 set to XS4513010LMB1AM42H
Jan 25 00:00:00 runsvdir: set clock 10, freq 200000000
Jan 25 00:00:00 runsvdir: [usb_gadget_client] ERROR nr_usb_gadget.cpp getService:79: Retrying to get UsbGadgetService
Jan 25 00:00:00 runsvdir: Property ro.bsp.glasses_sn1 set to A1#H1X652H178615M
Jan 25 00:00:00 runsvdir: [usb_gadget_client] ERROR nr_usb_gadget.cpp getService:79: Retrying to get UsbGadgetService
Jan 25 00:00:00 runsvdir: Property ro.bsp.hw_version set to GF_6
Jan 25 00:00:00 runsvdir: OK
Jan 25 00:00:00 runsvdir: USB_CONFIGFS: start ...
Jan 25 00:00:00 runsvdir: OK
Jan 25 00:00:00 runsvdir: set clock 13, freq 300000000
Jan 25 00:00:00 runsvdir: set clock 65, freq 300000000
Jan 25 00:00:00 runsvdir: set clock 71, freq 300000000
Jan 25 00:00:00 runsvdir: USB OS DESC
Jan 25 00:00:00 runsvdir: Set CPU frequency to 1296000 for mode middle
Jan 25 00:00:00 runsvdir: set S_VDD_0V8 and S_VDD_DOV8 to 787.5mV
Jan 25 00:00:00 runsvdir: USB_CONFIGFS: HID hid_index 0 func_index 1
Jan 25 00:00:00 runsvdir: USB_CONFIGFS: NCM ncm_index 0 func_index 2
Jan 25 00:00:00 runsvdir: USB_CONFIGFS: ECM ecm_index 0 func_index 3
Jan 25 00:00:00 runsvdir: USB_CONFIGFS: UAC uac1_index 0 func_index 4
Jan 25 00:00:00 runsvdir: USB_CONFIGFS: HID hid_index 1 func_index 5
Jan 25 00:00:00 runsvdir: USB_CONFIGFS: MTP mtp_index 0 func_index 6
Jan 25 00:00:00 runsvdir: ssize_init:data[0]: 4
Jan 25 00:00:00 runsvdir: ssize_init:data[0]: 4
Jan 25 00:00:00 runsvdir: [audio service]:audio service running
Jan 25 00:00:00 runsvdir: svcmgr: add_service('sys_cmd',4) uid=0 - ALREADY REGISTERED, OVERRIDE
Jan 25 00:00:00 runsvdir: USB_CONFIGFS: MASS_STORAGE mass_storage_index 0 func_index 7
Jan 25 00:00:00 runsvdir: svcmgr: add_service('rgn_cmd',1) uid=0 - ALREADY REGISTERED, OVERRIDE
Jan 25 00:00:00 runsvdir: svcmgr: add_service('vpss_cmd',2) uid=0 - ALREADY REGISTERED, OVERRIDE
Jan 25 00:00:00 runsvdir: USB_CONFIGFS: UVC uvc_index 0 func_index 8
Jan 25 00:00:00 runsvdir: 5hJk2Ld8Rt9sFpQw^M
Jan 25 00:00:00 runsvdir: [audio service]:AR_MPI_SYS_Init su
Jan 25 00:00:00 runsvdir: [audio service]:[audio]:create ipc channel success!
Jan 25 00:00:00 runsvdir: [audio service]:[audio]:added service sucess
Jan 25 00:00:00 runsvdir: [audio service]:ada checked 0
Jan 25 00:00:00 runsvdir: burstnum:15 mode:xreal0 payload:262144
Jan 25 00:00:00 runsvdir: ln: h264: No such file or directory
Jan 25 00:00:00 runsvdir: USB_CONFIGFS: UVC uvc_index 1 func_index 9
Jan 25 00:00:00 runsvdir: burstnum:15 mode:xreal1 payload:262144
Jan 25 00:00:00 runsvdir: /etc/usb_gadget_configfs.sh: line 1362: can't create functions/uvc.usb1/streaming/uncompressed/grey/guidFormat: nonexistent directory
Jan 25 00:00:00 runsvdir: Run usb_gadget service.
Jan 25 00:00:00 runsvdir: Run storage service.
Jan 25 00:00:00 runsvdir: shell config usb0
Jan 25 00:00:00 runsvdir: [usb_gadget_service] DEBUG usb_gadget.c get_usb_gadget_dir:90: usb_gadget_dir:/sys/kernel/config/usb_gadget/g0
Jan 25 00:00:00 runsvdir: [usb_gadget_service] DEBUG usb_gadget.c get_udc_dir:109: udc_dir:/sys/kernel/config/usb_gadget/g0/UDC
Jan 25 00:00:00 runsvdir: [usb_gadget_service] DEBUG usb_gadget.c get_config_dir:137: config_dir:/sys/kernel/config/usb_gadget/g0/configs/b.1
Jan 25 00:00:00 runsvdir: Property sys.bsp.ncm set to true
Jan 25 00:00:00 runsvdir: [storage_service] DEBUG storage.c get_gadget_dir:44: usb_gadget_dir:/sys/kernel/config/usb_gadget/g0
Jan 25 00:00:00 runsvdir: [storage_service] DEBUG storage.c get_udc_dir:63: udc_dir:/sys/kernel/config/usb_gadget/g0/UDC
Jan 25 00:00:00 runsvdir: [storage_service] DEBUG storage.c get_mtp_dir:75: mtp_dir:/sys/kernel/config/usb_gadget/g0/functions/ffs.mtp
Jan 25 00:00:00 runsvdir: [storage_service] DEBUG storage.c get_ums_dir:88: ums_dir:/sys/kernel/config/usb_gadget/g0/functions/mass_storage.usb0
Jan 25 00:00:00 runsvdir: [storage_service] DEBUG storage.c get_ums_dir:90: ums_lun_dir:/sys/kernel/config/usb_gadget/g0/functions/mass_storage.usb0/lun.0/file
Jan 25 00:00:00 runsvdir: [storage_service] DEBUG storage.c get_config_dir:118: config_dir:/sys/kernel/config/usb_gadget/g0/configs/b.1
Jan 25 00:00:00 runsvdir: [storage_service] DEBUG storage.c get_storage_func_dir:237: storage_func_dir:/sys/kernel/config/usb_gadget/g0/configs/b.1/f1
Jan 25 00:00:00 runsvdir: [storage_service] DEBUG storage.c init_dir:353: product_name:XREAL One
Jan 25 00:00:00 runsvdir: [storage_service] DEBUG storage.c init_dir:367: manufacture_name:XREAL
Jan 25 00:00:00 runsvdir: [storage_service] DEBUG storage.c init_dir:370: format_cmd:/bin/exfat/mkfs.exfat /dev/mmcblk0p22 -n "XREAL"
Jan 25 00:00:00 runsvdir: shell config usb1
Jan 25 00:00:00 runsvdir: [audio service]:ada checked 0
Jan 25 00:00:00 runsvdir: Property sys.bsp.ecm set to true
Jan 25 00:00:00 runsvdir: =======++++++++++++++++++===========
Jan 25 00:00:00 runsvdir: true true
Jan 25 00:00:00 runsvdir: =======++++++++++++++++++===========
Jan 25 00:00:00 runsvdir: =======++++++++++++++++++===========
Jan 25 00:00:00 runsvdir: start dhcpd
Jan 25 00:00:00 runsvdir: =======++++++++++++++++++===========
Jan 25 00:00:00 runsvdir: USB_CONFIGFS: finished ...
Jan 25 00:00:00 runsvdir: Run Dhcp service.
Jan 25 00:00:00 runsvdir: svcmgr: add_service('sys_cmd',7) uid=0 - ALREADY REGISTERED, OVERRIDE
Jan 25 00:00:00 runsvdir: svcmgr: add_service('rgn_cmd',4) uid=0 - ALREADY REGISTERED, OVERRIDE
Jan 25 00:00:00 runsvdir: svcmgr: add_service('vpss_cmd',1) uid=0 - ALREADY REGISTERED, OVERRIDE
Jan 25 00:00:00 runsvdir: ^[[0;31mDisplayService E: parse_default_config_file[499] Invalid JSON format^[[0;39m
Jan 25 00:00:00 runsvdir: ^[[0;31mDisplayService E: screen_probe[545] Failed to parse and update screen default config.^[[0;39m
Jan 25 00:00:00 runsvdir: Internet Systems Consortium DHCP Server 4.4.3-P1
Jan 25 00:00:00 runsvdir: Copyright 2004-2022 Internet Systems Consortium.
Jan 25 00:00:00 runsvdir: All rights reserved.
Jan 25 00:00:00 runsvdir: For info, please visit https://www.isc.org/software/dhcp/
Jan 25 00:00:00 runsvdir: Config file: /etc/dhcpd.conf
Jan 25 00:00:00 runsvdir: Database file: /tmp/dhcpd.lease
Jan 25 00:00:00 runsvdir: PID file: /tmp/dhcpd.pid
Jan 25 00:00:00 runsvdir: Wrote 0 leases to leases file.
Jan 25 00:00:00 runsvdir: ^[[36;22mI/[25 00:00:00.878831] [Scam]: (73 main)--- media service start --- ^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.879339] [Scam]: (77 main)Creat thread: thread_display_service. device:lvds @5lane2^[[0m
Jan 25 00:00:00 runsvdir: ^[[32;22mD/[25 00:00:00.879704] [Scam]: (79 main)Creat thread: thread_camera_service.^[[0m
Jan 25 00:00:00 runsvdir: DisplayService I : <timestamp boot up> -- [ 1.319 ]
Jan 25 00:00:00 runsvdir: DisplayService I : we will run display service (lvds@5lanesx2)
Jan 25 00:00:00 runsvdir: DisplayService I : <------- init display service ------->
Jan 25 00:00:00 runsvdir: read vb config data index:4 success.
Jan 25 00:00:00 runsvdir: DisplayService I : config pixel format YUV420p
Jan 25 00:00:00 runsvdir: DisplayService I : use 5lane2 port
Jan 25 00:00:00 runsvdir: DisplayService I : vo0 port index = 0x03
Jan 25 00:00:00 runsvdir: DisplayService I : use 5lane2 port
Jan 25 00:00:00 runsvdir: DisplayService I : vo1 port index = 0x0c
Jan 25 00:00:00 runsvdir: ^[[36;22mI/[25 00:00:00.884731] [Scam]: (804 XR_COMM_SYS_Init)camera vb pool is already configed.^[[0m
Jan 25 00:00:00 runsvdir: 5hJk2Ld8Rt9sFpQw^M
Jan 25 00:00:00 runsvdir: SS-COMMON-0: config vb success!DisplayService I : get product_code:GF
Jan 25 00:00:00 runsvdir: DisplayOLED I : Detected OLED model name (right): ecx343
Jan 25 00:00:00 runsvdir: DisplayOLED I : Detected OLED model name (left): ecx343
Jan 25 00:00:00 runsvdir: DisplayOLED I : screen fds opened successfully.
Jan 25 00:00:00 runsvdir: DisplayOLED I : screen soft reset in probe successfully!
Jan 25 00:00:00 runsvdir: DisplayOLED I : Left OLED model name: ecx343
Jan 25 00:00:00 runsvdir: DisplayOLED I : Right OLED model name: ecx343
Jan 25 00:00:00 runsvdir: DisplayOLED I : Screen name updated to: ecx343
Jan 25 00:00:00 runsvdir: DisplayOLED I : Calibrated Data Version: V1
Jan 25 00:00:00 runsvdir: DisplayOLED I : Orbit Horizon Data: 0
Jan 25 00:00:00 runsvdir: DisplayOLED I : Orbit Vertical Data: 0
Jan 25 00:00:00 runsvdir: DisplayOLED I : Orbit Horizon Data: 0
Jan 25 00:00:00 runsvdir: DisplayOLED I : Orbit Vertical Data: 0
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[0]: 127
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[1]: 28
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[2]: 26
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[3]: 29
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[4]: 23
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[5]: 14
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[6]: 124
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[7]: 0
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[8]: 28
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[9]: 31
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[10]: 37
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[11]: 28
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[12]: 17
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[13]: 0
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[14]: 6
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[15]: 49
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[16]: 55
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[17]: 59
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[18]: 51
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[19]: 35
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[20]: 9
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[0]: 127
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[1]: 28
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[2]: 35
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[3]: 52
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[4]: 50
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[5]: 45
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[6]: 18
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[7]: 0
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[8]: 30
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[9]: 43
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[10]: 68
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[11]: 65
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[12]: 56
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[13]: 26
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[14]: 6
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[15]: 58
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[16]: 77
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[17]: 94
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[18]: 90
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[19]: 82
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[20]: 41
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[0]: 127
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[1]: 22
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[2]: 16
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[3]: 15
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[4]: 6
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[5]: 249
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[6]: 124
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[7]: 0
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[8]: 24
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[9]: 20
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[10]: 21
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[11]: 10
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[12]: 0
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[13]: 0
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[14]: 6
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[15]: 41
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[16]: 41
Jan 25 00:00:00 runsvdir: Displ[audio service]:ada checked 0
Jan 25 00:00:00 runsvdir: binder: BR_SPAWN_LOOPER
Jan 25 00:00:00 runsvdir: binder: BR_SPAWN_LOOPER
Jan 25 00:00:00 runsvdir: binder: BR_SPAWN_LOOPER
Jan 25 00:00:00 runsvdir: binder: BR_SPAWN_LOOPER
Jan 25 00:00:00 runsvdir: binder: BR_SPAWN_LOOPER
Jan 25 00:00:00 runsvdir: ayOLED I : Gamma High Brightness[17]: 45
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[18]: 28
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[19]: 18
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma High Brightness[20]: 9
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[0]: 127
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[1]: 22
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[2]: 25
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[3]: 35
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[4]: 34
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[5]: 30
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[6]: 13
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[7]: 0
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[8]: 25
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[9]: 32
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[10]: 49
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[11]: 46
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[12]: 39
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[13]: 17
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[14]: 6
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[15]: 46
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[16]: 64
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[17]: 75
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[18]: 72
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[19]: 65
Jan 25 00:00:00 runsvdir: DisplayOLED I : Gamma Low Brightness[20]: 32
Jan 25 00:00:00 runsvdir: DisplayOLED I : XY Calibrated Data X[0]: -40, Y[0]: -45
Jan 25 00:00:00 runsvdir: DisplayOLED I : XY Calibrated Data X[1]: -16, Y[1]: -16
Jan 25 00:00:00 runsvdir: DisplayOLED I : XY Calibrated Data X[2]: -18, Y[2]: -18
Jan 25 00:00:00 runsvdir: DisplayOLED I : XY Calibrated Data X[3]: -19, Y[3]: -21
Jan 25 00:00:00 runsvdir: DisplayOLED I : XY Calibrated Data X[4]: -21, Y[4]: -22
Jan 25 00:00:00 runsvdir: DisplayOLED I : XY Calibrated Data X[5]: -19, Y[5]: -21
Jan 25 00:00:00 runsvdir: DisplayOLED I : XY Calibrated Data X[6]: -20, Y[6]: -20
Jan 25 00:00:00 runsvdir: DisplayOLED I : XY Calibrated Data X[7]: -20, Y[7]: -21
Jan 25 00:00:00 runsvdir: DisplayOLED I : XY Calibrated Data X[8]: -20, Y[8]: -20
Jan 25 00:00:00 runsvdir: DisplayOLED I : XY Calibrated Data X[9]: -20, Y[9]: -20
Jan 25 00:00:00 runsvdir: DisplayOLED I : XY Calibrated Data X[0]: -34, Y[0]: -47
Jan 25 00:00:00 runsvdir: DisplayOLED I : XY Calibrated Data X[1]: -12, Y[1]: -19
Jan 25 00:00:00 runsvdir: DisplayOLED I : XY Calibrated Data X[2]: -16, Y[2]: -22
Jan 25 00:00:00 runsvdir: DisplayOLED I : XY Calibrated Data X[3]: -16, Y[3]: -24
Jan 25 00:00:00 runsvdir: DisplayOLED I : XY Calibrated Data X[4]: -18, Y[4]: -25
Jan 25 00:00:00 runsvdir: DisplayOLED I : XY Calibrated Data X[5]: -18, Y[5]: -24
Jan 25 00:00:00 runsvdir: DisplayOLED I : XY Calibrated Data X[6]: -18, Y[6]: -23
Jan 25 00:00:00 runsvdir: DisplayOLED I : XY Calibrated Data X[7]: -18, Y[7]: -23
Jan 25 00:00:00 runsvdir: DisplayOLED I : XY Calibrated Data X[8]: -18, Y[8]: -22
Jan 25 00:00:00 runsvdir: DisplayOLED I : XY Calibrated Data X[9]: -20, Y[9]: -23
Jan 25 00:00:00 runsvdir: DisplayOLED I : oled_calibrated_data is ok
Jan 25 00:00:00 runsvdir: DisplayOLED I : Successfully parsed and updated OLED calibration data.
Jan 25 00:00:00 runsvdir: DisplayOLED I : OLED no set force lane!
Jan 25 00:00:00 runsvdir: DisplayOLED I : GF screen set inverse scan direction
Jan 25 00:00:00 runsvdir: DisplayOLED I : Set ecx34x_screen brightness:122.
Jan 25 00:00:00 runsvdir: DisplayOLED I : Set ecx34x_screen duty:95.
Jan 25 00:00:00 runsvdir: DisplayOLED I : Set oled_left x:-13,y:-18.
Jan 25 00:00:00 runsvdir: DisplayOLED I : Set oled_right x:-13,y:-18.
Jan 25 00:00:00 runsvdir: DisplayOLED I : screen set config parameters successfully!
Jan 25 00:00:00 runsvdir: DisplayService I : start task_main thread
Jan 25 00:00:00 runsvdir: DisplayService I : dp_rx_get_traning_status not support now
Jan 25 00:00:00 runsvdir: DisplayService I : get training status failed
Jan 25 00:00:00 runsvdir: DisplayService I : dp_rx_get_hdcp_status not support now
Jan 25 00:00:00 runsvdir: DisplayService I : get hdcp status failed
Jan 25 00:00:00 runsvdir: DisplayService I : dp monitor start
Jan 25 00:00:00 runsvdir: DisplayService I : run send edid thread
Jan 25 00:00:00 runsvdir: DisplayService I : run dpisp sate machine, current_state=0
Jan 25 00:00:00 runsvdir: DisplayService I : dpisp get a new state:1
Jan 25 00:00:00 runsvdir: DisplayService D : get dprx so
Jan 25 00:00:00 runsvdir: DisplayService D : wait for client
Jan 25 00:00:00 runsvdir: DisplayService I : Get frame stats: Successes 0.00, Failures 0.00
Jan 25 00:00:00 runsvdir: DisplayService I : dpvsync : 0.00
Jan 25 00:00:00 runsvdir: DisplayService I : -- RUN COMMAND THREAD --
Jan 25 00:00:00 runsvdir: DisplayService I : debug-command listen...
Jan 25 00:00:00 runsvdir: DisplayService D : init pipi = 2, isp object = 0x7f91f88108
Jan 25 00:00:00 runsvdir: DisplayService I : enPixelFormat:0x17
Jan 25 00:00:00 runsvdir: DisplayService I : getpro capture_vio_key: false
Jan 25 00:00:00 runsvdir: DisplayService I : get dpvif_fre_hz = 0
Jan 25 00:00:00 runsvdir: SS-COMMON-1: config dev colock success!DisplayService I : >>>>>> stComboAttr >>>>>>>
Jan 25 00:00:00 runsvdir: DisplayService I : devno: 0
Jan 25 00:00:00 runsvdir: DisplayService I : input_mode: 0x0b
Jan 25 00:00:00 runsvdir: DisplayService I : data_rate : 450
Jan 25 00:00:00 runsvdir: DisplayService I : mipi-input_data_type: 0x02
Jan 25 00:00:00 runsvdir: DisplayService I : stSnsSize.u32Width = 1920
Jan 25 00:00:00 runsvdir: DisplayService I : stSnsSize.u32Height = 1080
Jan 25 00:00:00 runsvdir: DisplayService I : stTiming.hblank = 280
Jan 25 00:00:00 runsvdir: DisplayService I : stTiming.vblank = 45
Jan 25 00:00:00 runsvdir: DisplayService I : stChnAttr.stSize.u32Width = 1920
Jan 25 00:00:00 runsvdir: DisplayService I : stChnAttr.stSize.u32Height = 1080
Jan 25 00:00:00 runsvdir: DisplayService I : stChnAttr.u32BufCount = 8
Jan 25 00:00:00 runsvdir: DisplayService I : stPubAttr.f32FrameRate = 0.000000
Jan 25 00:00:00 runsvdir: DisplayService I : <<<<<< stComboAttr <<<<<<<
Jan 25 00:00:00 runsvdir: read vb config data index:4 success.
Jan 25 00:00:00 runsvdir: DisplayService I : pipe:2 channel:0 get vb pool id <4>!
Jan 25 00:00:00 runsvdir: read vb config data index:4 success.
Jan 25 00:00:00 runsvdir: get vb pool id <4>!
Jan 25 00:00:00 runsvdir: DisplayService I : pipe:2 chn:0 bind vb pool:4 success!
Jan 25 00:00:00 runsvdir: camera camera camera camera 245 is not in position. is not in position. is not in position.
Jan 25 00:00:00 runsvdir: 3 is not in position.
Jan 25 00:00:00 runsvdir: camera_plug_detect 0
Jan 25 00:00:00 runsvdir: read imx681_plug_in_out gpio level [0] : 240
Jan 25 00:00:00 runsvdir: Detect camera plug action!
Jan 25 00:00:00 runsvdir: read imx681_plug_in_out gpio level [0] : 240
Jan 25 00:00:00 runsvdir: Detect camera plug action!
Jan 25 00:00:00 runsvdir: read imx681_plug_in_out gpio level [0] : 1
Jan 25 00:00:00 runsvdir: gpio level value is 1, sum is 1, counter is 1.
Jan 25 00:00:00 runsvdir: read imx681_plug_in_out gpio level [0] : 1
Jan 25 00:00:00 runsvdir: gpio level value is 1, sum is 2, counter is 2.
Jan 25 00:00:00 runsvdir: read imx681_plug_in_out gpio level [0] : 1
Jan 25 00:00:00 runsvdir: gpio level value is 1, sum is 3, counter is 3.
Jan 25 00:00:00 runsvdir: read imx681_plug_in_out gpio level [0] : 1
Jan 25 00:00:00 runsvdir: gpio level value is 1, sum is 4, counter is 4.
Jan 25 00:00:00 runsvdir: read imx681_plug_in_out gpio level [0] : 1
Jan 25 00:00:00 runsvdir: gpio level value is 1, sum is 5, counter is 5.
Jan 25 00:00:00 runsvdir: read imx681_plug_in_out gpio level [0] : 1
Jan 25 00:00:00 runsvdir: gpio level value is 1, sum is 6, counter is 6.
Jan 25 00:00:00 runsvdir: read imx681_plug_in_out gpio level [0] : 1
Jan 25 00:00:00 runsvdir: gpio level value is 1, sum is 7, counter is 7.
Jan 25 00:00:00 runsvdir: read imx681_plug_in_out gpio level [0] : 1
Jan 25 00:00:00 runsvdir: gpio level value is 1, sum is 8, counter is 8.
Jan 25 00:00:00 runsvdir: read imx681_plug_in_out gpio level [0] : 255
Jan 25 00:00:00 runsvdir: gpio level sum is 8, counter is 8.
Jan 25 00:00:00 runsvdir: Detect camera plug in.
Jan 25 00:00:00 runsvdir: Listening on LPF/usb1/fc:d2:b6:ad:cc:6b/169.254.1.0/24
Jan 25 00:00:00 runsvdir: Sending on   LPF/usb1/fc:d2:b6:ad:cc:6b/169.254.1.0/24
Jan 25 00:00:00 runsvdir: read imx681_plug_in_out gpio level [Detect GEM plug in/out event. plug_in_stat:01] : 240
Jan 25 00:00:00 runsvdir: Detect camera plug action!
Jan 25 00:00:00 runsvdir: read imx681_plug_in_out gpio level [0] : 240
Jan 25 00:00:00 runsvdir: Detect camera plug action!
Jan 25 00:00:00 runsvdir: read imx681_plug_in_out gpio level [0] : Detect GEM plug in/out event. plug_in_stat:1
Jan 25 00:00:00 runsvdir: 1gpio level value is 1, sum is 1, counter is 1.
Jan 25 00:00:00 runsvdir: 1.
Jan 25 00:00:00 runsvdir: Camera custominfo init.
Jan 25 00:00:00 runsvdir: find_stream_attr failed! STREAM_TYPE: 0
Jan 25 00:00:00 runsvdir: find_stream_attr failed! STREAM_TYPE: 0
Jan 25 00:00:00 runsvdir: stream_control 1
Jan 25 00:00:01 runsvdir: DisplayService I : isp attr: type : bt709, color range : full range
Jan 25 00:00:01 runsvdir: DisplayService I : <timestamp init vi done> -- [ 1.424 ]
Jan 25 00:00:01 runsvdir: DisplayService I : current dpisp status update :1
Jan 25 00:00:01 runsvdir: read imx681_plug_in_out gpio level [0] : 1
Jan 25 00:00:01 runsvdir: [audio service]:ada checked 0
Jan 25 00:00:01 runsvdir: Listening on LPF/usb0/fc:d2:b6:ad:cc:6a/169.254.2.0/24
Jan 25 00:00:01 runsvdir: Sending on   LPF/usb0/fc:d2:b6:ad:cc:6a/169.254.2.0/24
Jan 25 00:00:01 runsvdir: Sending on   Socket/fallback/fallback-net
Jan 25 00:00:01 runsvdir: gpio level value is 1, sum is 2, counter is 2.
Jan 25 00:00:01 runsvdir: The checksum successfully matched, checksum_calc=0x51, checksum_read=0x51
Jan 25 00:00:01 runsvdir: json file len:447Close camera i2c /dev/i2c-4 sucess.
Jan 25 00:00:01 runsvdir: DisplayService I : <timestamp get a video change interrupt> -- [ 1.480 ]
Jan 25 00:00:01 runsvdir: DisplayService I : pixel clock: 222750000, fps = 90
Jan 25 00:00:01 runsvdir: The checksum successfully matched, checksum_calc=0xff, checksum_read=0xff
Jan 25 00:00:01 runsvdir: Close camera i2c /dev/i2c-4 sucess.
Jan 25 00:00:01 runsvdir: The checksum successfully matched, checksum_calc=0x43, checksum_read=0x43
Jan 25 00:00:01 runsvdir: Close camera i2c /dev/i2c-4 sucess.
Jan 25 00:00:01 runsvdir: GEM SN:<A1#Z1X655T11861JD>
Jan 25 00:00:01 runsvdir: GEM BarCode:<GCD2S1L14ZEB003014>
Jan 25 00:00:01 runsvdir: read vb config data index:2 success.
Jan 25 00:00:01 runsvdir: Create Imx681 image.
Jan 25 00:00:01 runsvdir: stream_control 0
Jan 25 00:00:01 runsvdir: read imx681_plug_in_out gpio level [0] : 1
Jan 25 00:00:01 runsvdir: binder: BR_SPAWN_LOOPER
Jan 25 00:00:01 runsvdir: [audio service]:[audio]:handleClientRequest cmd[1]
Jan 25 00:00:01 runsvdir: [audio service]:ada checked 0
Jan 25 00:00:01 runsvdir: gpio level value is 1, sum is 3, counter is 3.
Jan 25 00:00:01 runsvdir: DisplayService I : __handleClientRequest[1519] recv data: connect service 
Jan 25 00:00:01 runsvdir: DisplayService I : REGISTER_CALLBACK, callback - displayservice_callback
Jan 25 00:00:01 runsvdir: read imx681_plug_in_out gpio level [0] : 1
Jan 25 00:00:01 runsvdir: gpio level value is 1, sum is 4, counter is 4.
Jan 25 00:00:01 runsvdir: Current connected miscClientServer clientNum is 0
Jan 25 00:00:01 runsvdir: Read camera serial number.
Jan 25 00:00:01 runsvdir: The checksum successfully matched, checksum_calc=0xff, checksum_read=0xff
Jan 25 00:00:01 runsvdir: Close camera i2c /dev/i2c-4 sucess.
Jan 25 00:00:01 runsvdir: camera_custom_sn_read success, SN:<A1#Z1X655T11861JD>DISCONNECT miscClientServer clientNum is 1
Jan 25 00:00:01 runsvdir: Misc client don't need to unregister callback.
Jan 25 00:00:01 runsvdir: [audio service]:ada checked 0
Jan 25 00:00:01 runsvdir: read imx681_plug_in_out gpio level [0] : 1
Jan 25 00:00:01 runsvdir: gpio level value is 1, sum is 5, counter is 5.
Jan 25 00:00:01 runsvdir: DisplayService I : <timestamp DP status changed start> -- [ 1.680 ]
Jan 25 00:00:01 runsvdir: DisplayService I : dp status changed, 0x0@0 -> 1920x1080@90
Jan 25 00:00:01 runsvdir: DisplayService I : <timestamp DP status changed end> -- [ 1.680 ]
Jan 25 00:00:01 runsvdir: DisplayService I : notify dp info update
Jan 25 00:00:01 runsvdir: DisplayService I : get a dp node, state 1
Jan 25 00:00:01 runsvdir: DisplayService I : should init mppvi
Jan 25 00:00:01 runsvdir: DisplayService I : broadcast dp event : 1
Jan 25 00:00:01 runsvdir: DisplayService I : release this event
Jan 25 00:00:01 runsvdir: DisplayService I : get edid 9, dp_audio 0
Jan 25 00:00:01 runsvdir: DisplayService I : current vo's refresh rate 90Hz, return 3
Jan 25 00:00:01 runsvdir: DisplayService I : get edid 9, dp_audio 0
Jan 25 00:00:01 runsvdir: read imx681_plug_in_out gpio level [0] : 1
Jan 25 00:00:01 runsvdir: [audio service]:ada checked 0
Jan 25 00:00:01 runsvdir: gpio level value is 1, sum is 6, counter is 6.
Jan 25 00:00:01 runsvdir: read imx681_plug_in_out gpio level [0] : 1
Jan 25 00:00:01 runsvdir: [audio service]:ada checked 0
Jan 25 00:00:01 runsvdir: gpio level value is 1, sum is 7, counter is 7.
Jan 25 00:00:01 runsvdir: read imx681_plug_in_out gpio level [0] : 1
Jan 25 00:00:01 runsvdir: gpio level value is 1, sum is 8, counter is 8.
Jan 25 00:00:01 runsvdir: read imx681_plug_in_out gpio level [0] : 255
Jan 25 00:00:01 runsvdir: gpio level sum is 8, counter is 8.
Jan 25 00:00:01 runsvdir: Detect camera plug in.
Jan 25 00:00:01 runsvdir: [audio service]:[audio]:handleClientRequest cmd[7]
Jan 25 00:00:01 runsvdir: [audio service]:[audio binder service]:GET_CURRENT_MODE GET_CURRENT_MODE[0]
Jan 25 00:00:01 runsvdir: [audio service]:ada checked 1
Jan 25 00:00:01 runsvdir: [audio service]:ada checked done
Jan 25 00:00:01 runsvdir: get capture mask: 3
Jan 25 00:00:01 runsvdir: [audio service]:dp_audio_only:0
Jan 25 00:00:01 runsvdir: [audio service]:uac vb pool is already configed.
Jan 25 00:00:01 runsvdir: read vb config data index:0 success.
Jan 25 00:00:01 runsvdir: [audio service]:Uac get vb pool id [0] success!
Jan 25 00:00:01 runsvdir: [audio service]:start UAC
Jan 25 00:00:01 runsvdir: get playback mask: 3
Jan 25 00:00:01 runsvdir: [audio service]:buffer malloc success size[4096]!
Jan 25 00:00:01 runsvdir: [audio service]:AR_MPI_AO_GetPubAttr enSamplerate:48000
Jan 25 00:00:01 runsvdir: [audio service]:UVC_AUDIO_Ao_send run
Jan 25 00:00:01 runsvdir: [audio service]:latency=10666
Jan 25 00:00:01 runsvdir: [audio service]:UVC_AUDIO_Ao run
Jan 25 00:00:01 runsvdir: [audio service]:buffer_size 1024
Jan 25 00:00:01 runsvdir: [audio service]:period_size 256
Jan 25 00:00:01 runsvdir: [audio service]:app_ring_buffer_readn buff_size[2048] s32Ret[0]
Jan 25 00:00:01 runsvdir: [audio service]:snd_ctl_volume_thread[0] run 
Jan 25 00:00:01 runsvdir: [audio service]:HW info suc: 0
Jan 25 00:00:01 runsvdir: [audio service]:  card - 0
Jan 25 00:00:01 runsvdir: [audio service]:  id - 'UAC1Gadget'
Jan 25 00:00:01 runsvdir: [audio service]:  driver - 'UAC1_Gadget'
Jan 25 00:00:01 runsvdir: [audio service]:  name - 'UAC1_Gadget'
Jan 25 00:00:01 runsvdir: [audio service]:  longname - 'UAC1_Gadget 0'
Jan 25 00:00:01 runsvdir: [audio service]:  mixername - 'UAC1_Gadget'
Jan 25 00:00:01 runsvdir: [audio service]:  components - ''
Jan 25 00:00:01 runsvdir: [audio service]:elem list count=7
Jan 25 00:00:01 runsvdir: [audio service]:numid=1, name='Playback Pitch 1000000'
Jan 25 00:00:01 runsvdir: [audio service]:numid=2, name='PCM Playback Switch'
Jan 25 00:00:01 runsvdir: [audio service]:numid=3, name='PCM Playback Volume'
Jan 25 00:00:01 runsvdir: [audio service]:numid=4, name='Playback Rate'
Jan 25 00:00:01 runsvdir: [audio service]:numid=5, name='PCM Capture Switch'
Jan 25 00:00:01 runsvdir: [audio service]:numid=6, name='PCM Capture Volume'
Jan 25 00:00:01 runsvdir: [audio service]:numid=7, name='Capture Rate'
Jan 25 00:00:01 runsvdir: [audio service]:snd_numid_handle :5 1
Jan 25 00:00:01 runsvdir: [audio service]:open /dev/smartPA_L su
Jan 25 00:00:01 runsvdir: [audio service]:open /dev/smartPA_R su
Jan 25 00:00:01 runsvdir: [audio service]:UVC_AUDIO_Ai run
Jan 25 00:00:01 runsvdir: [audio service]:UVC_AUDIO_Ai Thread is paused...
Jan 25 00:00:01 runsvdir: [audio service]:smartPA start su
Jan 25 00:00:01 runsvdir: [audio service]:snd_numid_handle :7 0
Jan 25 00:00:01 runsvdir: [audio service]:snd_numid_handle :6 0
Jan 25 00:00:01 runsvdir: [audio service]:hsv[0],index[0]dspv[1000]
Jan 25 00:00:01 runsvdir: [audio service]:set_dsp_volume open pipe file failed -1 -1 No such device or address
Jan 25 00:00:01 runsvdir: [audio service]:volume changing , volume is:1000 0
Jan 25 00:00:01 runsvdir: [audio service]:snd_numid_handle :4 48000
Jan 25 00:00:01 runsvdir: [audio service]:dsp_mode_without_mic :1
Jan 25 00:00:01 runsvdir: [audio service]:open pipe file failed -1 -1 No such device or address
Jan 25 00:00:01 runsvdir: [audio service]:dsp mode set 3
Jan 25 00:00:01 runsvdir: [audio service]:snd_ctl_event wait event
Jan 25 00:00:01 runsvdir: [audio service]:snd_numid_handle :4 48000
Jan 25 00:00:01 runsvdir: [audio service]:dsp_mode_without_mic :1
Jan 25 00:00:01 runsvdir: [audio service]:open pipe file failed -1 -1 No such device or address
Jan 25 00:00:01 runsvdir: [audio service]:dsp mode set 3
Jan 25 00:00:01 runsvdir: [audio service]:snd_numid_handle :4 0
Jan 25 00:00:01 runsvdir: [audio service]:dsp_mode_without_mic :1
Jan 25 00:00:01 runsvdir: [audio service]:open pipe file failed -1 -1 No such device or address
Jan 25 00:00:01 runsvdir: [audio service]:dsp mode set to 1
Jan 25 00:00:01 runsvdir: [audio service]:UVC_AUDIO_Ai Thread is paused...
Jan 25 00:00:01 runsvdir: svcmgr: add_service('sys_cmd',c) uid=0 - ALREADY REGISTERED, OVERRIDE
Jan 25 00:00:01 runsvdir: svcmgr: add_service('rgn_cmd',7) uid=0 - ALREADY REGISTERED, OVERRIDE
Jan 25 00:00:01 runsvdir: svcmgr: add_service('vpss_cmd',4) uid=0 - ALREADY REGISTERED, OVERRIDE
Jan 25 00:00:01 runsvdir: [audio service]:snd_numid_handle :6 -1
Jan 25 00:00:01 runsvdir: [audio service]:hsv[-1],index[0]dspv[1000]
Jan 25 00:00:01 runsvdir: [audio service]:set_dsp_volume open pipe file failed -1 -1 No such device or address
Jan 25 00:00:01 runsvdir: [audio service]:volume changing , volume is:1000 -1
Jan 25 00:00:01 runsvdir: [audio service]:snd_numid_handle :6 0
Jan 25 00:00:01 runsvdir: [audio service]:hsv[0],index[0]dspv[1000]
Jan 25 00:00:01 runsvdir: [audio service]:set_dsp_volume open pipe file failed -1 -1 No such device or address
Jan 25 00:00:01 runsvdir: [audio service]:volume changing , volume is:1000 0
Jan 25 00:00:01 runsvdir: [usb_gadget_client] ERROR nr_usb_gadget.cpp getService:79: Retrying to get UsbGadgetService
Jan 25 00:00:01 runsvdir: [usb_gadget_service] DEBUG Service.cpp onTransact:129: Received string: get config
Jan 25 00:00:01 runsvdir: [usb_gadget_service] ERROR Service.cpp parseUsbGadgetConfig:79: Invalid config segment: get config
Jan 25 00:00:01 runsvdir: [usb_gadget_service] DEBUG Service.cpp onTransact:139: usb gadget no change
Jan 25 00:00:01 runsvdir: [audio service]:snd_numid_handle :3 -1
Jan 25 00:00:01 runsvdir: [usb_gadget_client] DEBUG nr_usb_gadget.cpp get_usb_gadget_config:234: Configuration get: ncm=enable,ecm=enable,hid=enable,uac=enable
Jan 25 00:00:01 runsvdir: [audio service]:snd_numid_handle :3 0
Jan 25 00:00:01 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:01 runsvdir: 5hJk2Ld8Rt9sFpQw^M
Jan 25 00:00:01 runsvdir: 5hJk2Ld8Rt9sFpQw^M
Jan 25 00:00:01 runsvdir: 5hJk2Ld8Rt9sFpQw^M
Jan 25 00:00:01 runsvdir: ar_axi_dma_signal_semaphore_init success!
Jan 25 00:00:01 runsvdir: cjson_buffer is null ^M
Jan 25 00:00:01 runsvdir: vin start @ sdk version [5hJk2Ld8Rt9sFpQw] 
Jan 25 00:00:01 runsvdir: ^[[35;22m[124390387][0300][0x7f81f5f200][SYS_CORE][VI_KEY] server.c: start_camera_server : 3693 load vin driver start^[[0m
Jan 25 00:00:01 runsvdir: ^[[31;22m[124390390][0300][0x7f81f5f200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [/usrdata/local/factory/tunning/dpvif/dp_scaler_lut.bin] failed!^[[0m
Jan 25 00:00:01 runsvdir: open the driver sns:dp_rx obj:stSnsdp_rxObj lib:libsns_dp_rx.so 
Jan 25 00:00:01 runsvdir: ^[[35;22m[124390392][0300][0x7f81f5f200][SYS_CORE][VI_KEY] server.c: start_camera_server : 3957 load vin driver end max_sensor_count=5^[[0m
Jan 25 00:00:01 runsvdir: Create socked success
Jan 25 00:00:01 runsvdir: Bind socket success
Jan 25 00:00:01 runsvdir: Listen socket success
Jan 25 00:00:01 runsvdir: client accept thread running
Jan 25 00:00:01 runsvdir: ^[[31;22m[25 00:00:00.955031][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3559 !!!!!!the vif is force to dpcm duce to reduce bandwidth, if you want to use the dng, please set the pstPipeAttr->enEnableN
Jan 25 00:00:01 runsvdir: ^[[31;22m[25 00:00:00.955247][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3603 !!!!!!! Note: your app cfg the vif write compress on,so you must cfg the vif fre >=3*mipi fre !!!!!!^[[0m
Jan 25 00:00:01 runsvdir: ^[[31;22m[25 00:00:00.956173][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7846 not supported addr type^[[0m
Jan 25 00:00:01 runsvdir: ^[[31;22m[25 00:00:00.956211][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7856 not supported data type^[[0m
Jan 25 00:00:01 runsvdir: ^[[35;22m[124390393][0300][0x7f82382200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_open_camera : 920 camera_id=1^[[0m
Jan 25 00:00:01 runsvdir: ^[[35;22m[124390393][0300][0x7f812a9200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:00:01 runsvdir: ^[[35;22m[124390393][0300][0x7f812a9200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:01 runsvdir: ^[[35;22m[124390394][0300][0x7f813ae200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:01 runsvdir: ^[[35;22m[124390394][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:00:01 runsvdir: ^[[35;22m[124390394][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:00:01 runsvdir: ^[[35;22m[124390394][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:01 runsvdir: w h change from 0 0  to 1920 1080 fetch scaler lut 
Jan 25 00:00:01 runsvdir: ^[[31;22m[124390394][0300][0x7f813ae200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [/usrdata/local/factory/tunning/dpvif/dp_scaler_lut.bin] failed!^[[0m
Jan 25 00:00:01 runsvdir: out and in is equal, use y bypass lut table 
Jan 25 00:00:01 runsvdir: 36823000 36a41000 36aca000 0 0 0 36b4d000 36d6b000 36df4000 0 0 0 
Jan 25 00:00:01 runsvdir: aec index 1096686769 line 1125 gain 1.000000 
Jan 25 00:00:01 runsvdir: ^[[35;22m[124390394][0300][0x7f813ae200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:00:01 runsvdir: ^[[35;22m[124390394][0300][0x7f813ae200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:01 runsvdir: ^[[35;22m[124390394][0300][0x7f813ae200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:01 runsvdir: ^[[35;22m[124390395][0300][0x7f813ae200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:00:01 runsvdir: [ahb-3] start llp_pa:0x3cc0c000 llp:0x7f80a24000!
Jan 25 00:00:01 runsvdir: [ahb-3] src:0x3cc04000 dest:0x13111c8 ctl_l:0x18909125!
Jan 25 00:00:01 runsvdir: ^[[33;22m[124390450][0300][0x7f813ae200][AIO_CORE][WARN] i2s.c: ar_i2s_io_dev_dma_start : 1345 Ao s32ChnEnCnt = 1 u32ChnCnt 2.
Jan 25 00:00:01 runsvdir: ^[[0m
Jan 25 00:00:01 runsvdir: [ahb-4] start llp_pa:0x3cc1d000 llp:0x7f80a1b000!
Jan 25 00:00:01 runsvdir: [ahb-4] src:0x13111c0 dest:0x3cc0d000 ctl_l:0x1a209425!
Jan 25 00:00:01 runsvdir: ^[[33;22m[124390452][0300][0x7f812a9200][AIO_CORE][WARN] i2s.c: ar_i2s_io_dev_dma_start : 1345 Ai s32ChnEnCnt = 1 u32ChnCnt 2.
Jan 25 00:00:01 runsvdir: ^[[0m
Jan 25 00:00:01 runsvdir: ^[[33;22m[124390457][0300][0x7f81267200][VO_CORE][WARN] display_api.c: dc_init : 2258 use dc_reg_irq_by_cmd!^[[0m
Jan 25 00:00:01 runsvdir: ^[[31;22m[124390457][0300][0x7f81267200][VO_CORE][ERROR] display_top.c: ar_display_dev_enable : 3521 not support set irq_type:^[[33;22m[124390457][0300][0x7f80a45200][AXIMGR_CORE][WARN] ar_aximgr_lb.c: ar_aximgr_lb_enable : 285
Jan 25 00:00:01 runsvdir: ^[[33;22m[124390458][0300][0x7f813ae200][AXIMGR_CORE][WARN] ar_aximg[command_service] [20270125 000001.636] [common/common.c] [get_process_state-1089] : process dhcpd status : S
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.636] [common/common.c] [start_process_by_name-650] : process dhcpd is running
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.636] [command_service.c] [create_task-199] : prepare to create thread : 0, name : hid_data_process
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.637] [command_service.c] [create_task-199] : prepare to create thread : 1, name : tcp_data_process
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.637] [command_service.c] [create_task-199] : prepare to create thread : 2, name : hid_data_receive
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.637] [sub_thread/hid_receive_send_thread.c] [hid_data_receive_thread-118] : start
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.637] [command_service.c] [create_task-199] : prepare to create thread : 3, name : hid_data_send
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.637] [command_service.c] [create_task-199] : prepare to create thread : 4, name : restart_control
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.637] [command_service.c] [create_task-199] : prepare to create thread : 5, name : tcp_data_receive
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.638] [sub_thread/tcp_receive_send_thread.c] [tcp_data_receive_thread-26] : waiting connection...
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.638] [command_service.c] [create_task-199] : prepare to create thread : 6, name : tcp_data_send
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.638] [command_service.c] [create_task-199] : prepare to create thread : 7, name : dsp_upgrade
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.638] [command_service.c] [create_task-199] : prepare to create thread : 8, name : upgrade_monitor
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.638] [command_service.c] [create_task-199] : prepare to create thread : 9, name : usb_config
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.638] [hardware/DSP_IA8201/src/dsp_io_ctl.c] [dsp_io_init-23] : ret : 0, mode : 1
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.638] [hardware/DSP_IA8201/src/uart.c] [get_uart_tty_path-30] : tty path : /sys/devices/platform/soc/1504000.serial/tty
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.638] [hardware/DSP_IA8201/src/uart.c] [get_uart_tty_path-37] : .
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.638] [hardware/DSP_IA8201/src/uart.c] [get_uart_tty_path-37] : ..
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.638] [hardware/DSP_IA8201/src/uart.c] [get_uart_tty_path-37] : ttyS2
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.638] [hardware/DSP_IA8201/src/uart.c] [get_uart_tty_path-42] : name : ttyS2
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.638] [hardware/DSP_IA8201/src/uart.c] [get_uart_tty_path-46] : get uart node : /dev/ttyS2
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.642] [hardware/DSP_IA8201/src/uart.c] [get_uart_tty_path-54] : serial irq : 32
Jan 25 00:00:01 runsvdir: sh: can't create /proc/irq/32/smp_affinity: nonexistent directory
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.645] [hardware/DSP_IA8201/src/dsp_mode_set.c] [get_dsp_sysmode-810] : read mode : 2
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.645] [command_service.c] [create_task-199] : prepare to create thread : 10, name : cmd_audio
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.645] [function/upgrade_function.c] [read_system_version-579] : os version : 15.1.02.658_USERROOT
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.645] [command_service.c] [set_property_values-724] : version code str : 2658, version code : 2658
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.646] [function/upgrade_function.c] [read_dsp_version-1046] : lock dsp
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.647] [function/upgrade_function.c] [read_dsp_version-1052] : dsp version : 15.A.00.069_20241211
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.647] [common/common.c] [update_property_file-1314] : key offset [0], value offset [20] eof offset [41]
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.647] [common/common.c] [update_property_file-1338] : file [/persist/device.prop] key [sys.bsp.dsp_version] value is the same as setting [15.A.00.069_20241211]
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.647] [common/common.c] [update_property_file-1314] : key offset [41], value offset [60] eof offset [81]
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.647] [common/common.c] [update_property_file-1338] : file [/persist/device.prop] key [ro.bsp.dsp_version] value is the same as setting [15.A.00.069_20241211]
Jan 25 00:00:01 runsvdir: libc: Unable to set property "ro.bsp.dsp_version" to "15.A.00.069_20241211": error code: 0xb
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.648] [common/common.c] [update_property_file-1392] : set property [ro.bsp.dsp_version]=[15.A.00.069_20241211] failed
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.648] [upgrade/upgrade.c] [set_upgrade_progress-166] : [0 0]
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.648] [upgrade/upgrade.c] [set_upgrade_status-156] : set upgrade status : false
Jan 25 00:00:01 runsvdir: [audio service]:open pipe file failed -1 -1 No such device or address
Jan 25 00:00:01 runsvdir: [audio service]:dsp mode set fail[1]
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.657] [hardware/DSP_IA8201/src/dsp_mode_set.c] [Route_SetMode-472] : try lock dsp
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.670] [hardware/DSP_IA8201/src/dsp_mode_set.c] [Route_SetMode-509] : set mode [1], return [0]
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.670] [sub_thread/communicate_to_audio_service.c] [handle_audio_service_msg-126] : set dsp mode back over : 0
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.720] [hardware/DSP_IA8201/src/dsp_mode_set.c] [Route_GetMode-440] : try lock dsp
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.720] [hardware/DSP_IA8201/src/dsp_mode_set.c] [Route_GetMode-462] : get mode [1], return [0]
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.720] [sub_thread/communicate_to_audio_service.c] [handle_audio_service_msg-104] : get dsp mode back over : 1
Jan 25 00:00:01 runsvdir: [audio service]:read_dsp_characteristic reg[1][0] length[1]
Jan 25 00:00:01 runsvdir: [audio service]:set_dsp_mode result[0]
Jan 25 00:00:01 runsvdir: [audio service]:dsp mode set su[1]
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.770] [hardware/DSP_IA8201/src/dsp_mode_set.c] [route_set_volume-539] : setting volume 1000, register value 000be800
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.772] [hardware/DSP_IA8201/src/dsp_mode_set.c] [route_set_volume-552] : set volume [1000], return [0]
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.823] [hardware/DSP_IA8201/src/dsp_mode_set.c] [route_read_volume-560] : try lock dsp
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.823] [hardware/DSP_IA8201/src/dsp_mode_set.c] [route_read_volume-582] : read volume [1000], return [0]
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.823] [sub_thread/communicate_to_audio_service.c] [handle_audio_service_msg-191] : get dsp volume back over : 1000
Jan 25 00:00:01 runsvdir: [audio service]:read_dsp_characteristic reg[e8][3] length[2]
Jan 25 00:00:01 runsvdir: [audio service]:set_dsp_volume result[0] volume[1000]
Jan 25 00:00:01 runsvdir: [audio service]:dsp volume set su[1000]
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.873] [hardware/DSP_IA8201/src/dsp_mode_set.c] [Route_SetMode-472] : try lock dsp
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.885] [hardware/DSP_IA8201/src/dsp_mode_set.c] [Route_SetMode-509] : set mode [1], return [0]
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.886] [sub_thread/communicate_to_audio_service.c] [handle_audio_service_msg-126] : set dsp mode back over : 0
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.936] [hardware/DSP_IA8201/src/dsp_mode_set.c] [Route_GetMode-440] : try lock dsp
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.936] [hardware/DSP_IA8201/src/dsp_mode_set.c] [Route_GetMode-462] : get mode [1], return [0]
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.936] [sub_thread/communicate_to_audio_service.c] [handle_audio_service_msg-104] : get dsp mode back over : 1
Jan 25 00:00:01 runsvdir: [audio service]:read_dsp_characteristic reg[1][0] length[1]
Jan 25 00:00:01 runsvdir: [audio service]:set_dsp_mode result[0]
Jan 25 00:00:01 runsvdir: [audio service]:dsp mode set su[1]
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.986] [hardware/DSP_IA8201/src/dsp_mode_set.c] [route_set_volume-539] : setting volume 1000, register value 000be800
Jan 25 00:00:01 runsvdir: [command_service] [20270125 000001.989] [hardware/DSP_IA8201/src/dsp_mode_set.c] [route_set_volume-552] : set volume [1000], return [0]
Jan 25 00:00:02 runsvdir: [command_service] [20270125 000002.039] [hardware/DSP_IA8201/src/dsp_mode_set.c] [route_read_volume-560] : try lock dsp
Jan 25 00:00:02 runsvdir: [command_service] [20270125 000002.039] [hardware/DSP_IA8201/src/dsp_mode_set.c] [route_read_volume-582] : read volume [1000], return [0]
Jan 25 00:00:02 runsvdir: [command_service] [20270125 000002.039] [sub_thread/communicate_to_audio_service.c] [handle_audio_service_msg-191] : get dsp volume back over : 1000
Jan 25 00:00:02 runsvdir: [audio service]:read_dsp_characteristic reg[e8][3] length[2]
Jan 25 00:00:02 runsvdir: [audio service]:set_dsp_volume result[0] volume[1000]
Jan 25 00:00:02 runsvdir: [audio service]:dsp volume set su[1000]
Jan 25 00:00:02 runsvdir: [command_service] [20270125 000002.090] [hardware/DSP_IA8201/src/dsp_mode_set.c] [route_set_volume-539] : setting volume 1000, register value 000be800
Jan 25 00:00:02 runsvdir: [command_service] [20270125 000002.091] [hardware/DSP_IA8201/src/dsp_mode_set.c] [route_set_volume-552] : set volume [1000], return [0]
Jan 25 00:00:02 runsvdir: [audio service]:[audio]:handleClientRequest cmd[4]
Jan 25 00:00:02 runsvdir: [audio service]:[audio]:REGISTER_CALLBACK, callback - audioService_
Jan 25 00:00:02 runsvdir: [audio service]:[audio]:register [audioService_] successfully, callbackHandle = 1
Jan 25 00:00:02 runsvdir: [command_service] [20270125 000002.142] [hardware/DSP_IA8201/src/dsp_mode_set.c] [route_read_volume-560] : try lock dsp
Jan 25 00:00:02 runsvdir: [command_service] [20270125 000002.142] [hardware/DSP_IA8201/src/dsp_mode_set.c] [route_read_volume-582] : read volume [1000], return [0]
Jan 25 00:00:02 runsvdir: [command_service] [20270125 000002.142] [sub_thread/communicate_to_audio_service.c] [handle_audio_service_msg-191] : get dsp volume back over : 1000
Jan 25 00:00:02 runsvdir: [audio service]:read_dsp_characteristic reg[e8][3] length[2]
Jan 25 00:00:02 runsvdir: [audio service]:set_dsp_volume result[0] volume[1000]
Jan 25 00:00:02 runsvdir: [audio service]:dsp volume set su[1000]
Jan 25 00:00:02 runsvdir: [command_service] [20270125 000002.193] [hardware/DSP_IA8201/src/dsp_mode_set.c] [Route_SetMode-472] : try lock dsp
Jan 25 00:00:02 runsvdir: [command_service] [20270125 000002.205] [hardware/DSP_IA8201/src/dsp_mode_set.c] [Route_SetMode-509] : set mode [1], return [0]
Jan 25 00:00:02 runsvdir: [command_service] [20270125 000002.205] [sub_thread/communicate_to_audio_service.c] [handle_audio_service_msg-126] : set dsp mode back over : 0
Jan 25 00:00:02 runsvdir: [command_service] [20270125 000002.255] [hardware/DSP_IA8201/src/dsp_mode_set.c] [Route_GetMode-440] : try lock dsp
Jan 25 00:00:02 runsvdir: [command_service] [20270125 000002.255] [hardware/DSP_IA8201/src/dsp_mode_set.c] [Route_GetMode-462] : get mode [1], return [0]
Jan 25 00:00:02 runsvdir: [command_service] [20270125 000002.255] [sub_thread/communicate_to_audio_service.c] [handle_audio_service_msg-104] : get dsp mode back over : 1
Jan 25 00:00:02 runsvdir: [audio service]:read_dsp_characteristic reg[1][0] length[1]
Jan 25 00:00:02 runsvdir: [audio service]:set_dsp_mode result[0]
Jan 25 00:00:02 runsvdir: [audio service]:dsp mode set su[1]
Jan 25 00:00:02 runsvdir: DisplayService I : get edid 9, dp_audio 0
Jan 25 00:00:02 runsvdir: DisplayService I : current vo's refresh rate 90Hz, return 3
Jan 25 00:00:02 runsvdir: DisplayService I : get edid 9, dp_audio 0
Jan 25 00:00:02 runsvdir: DisplayService W : dpisp is not runing, can't get filter
Jan 25 00:00:02 runsvdir: DisplayService I : current vo's refresh rate 90Hz, return 3
Jan 25 00:00:02 runsvdir: DisplayService I : get edid 9, dp_audio 0
Jan 25 00:00:02 runsvdir: DisplayService I : <timestamp client connected> -- [ 2.27 ]
Jan 25 00:00:02 runsvdir: DisplayService I : added a client
Jan 25 00:00:02 runsvdir: DisplayService W : dpisp is not ready, it's going to init, current state = 1
Jan 25 00:00:02 runsvdir: DisplayService I : dpisp get a new state:2
Jan 25 00:00:02 runsvdir: DisplayService I : only enable dpisp
Jan 25 00:00:02 runsvdir: DisplayService I : <timestamp init isp > -- [ 2.44 ]
Jan 25 00:00:02 runsvdir: DisplayService I : enable vi channel output
Jan 25 00:00:02 runsvdir: DisplayService I : Set original LUT...
Jan 25 00:00:02 runsvdir: DisplayService I : <timestamp init isp done> -- [ 2.51 ]
Jan 25 00:00:02 runsvdir: DisplayService I : current dpisp status update :2
Jan 25 00:00:02 runsvdir: DisplayService I : get dp info 1920x1080@90
Jan 25 00:00:02 runsvdir: Current connected grayClientServer clientNum is 0
Jan 25 00:00:02 runsvdir: [command_service] [20270125 000002.877] [function/upgrade_function.c] [read_system_version-579] : os version : 15.1.02.658_USERROOT
Jan 25 00:00:02 runsvdir: [command_service] [20270125 000002.878] [common/common.c] [get_func_by_msgid-993] : unknown msgid : 00d4
Jan 25 00:00:02 runsvdir: [command_service] [20270125 000002.897] [function/upgrade_function.c] [read_system_version-579] : os version : 15.1.02.658_USERROOT
Jan 25 00:00:02 runsvdir: [command_service] [20270125 000002.898] [common/common.c] [get_func_by_msgid-993] : unknown msgid : 00d4
Jan 25 00:00:02 runsvdir: [command_service] [20270125 000002.898] [function/upgrade_function.c] [read_system_version-579] : os version : 15.1.02.658_USERROOT
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.145] [function/upgrade_function.c] [read_system_version-579] : os version : 15.1.02.658_USERROOT
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.145] [common/common.c] [get_func_by_msgid-993] : unknown msgid : 00d4
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.146] [common/common.c] [get_func_by_msgid-993] : unknown msgid : 0016
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.163] [function/upgrade_function.c] [read_system_version-579] : os version : 15.1.02.658_USERROOT
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.163] [common/common.c] [get_func_by_msgid-993] : unknown msgid : 00d4
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.164] [function/upgrade_function.c] [read_dsp_version-1046] : lock dsp
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.165] [function/upgrade_function.c] [read_dsp_version-1052] : dsp version : 15.A.00.069_20241211
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.165] [common/common.c] [update_property_file-1314] : key offset [0], value offset [20] eof offset [41]
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.165] [common/common.c] [update_property_file-1338] : file [/persist/device.prop] key [sys.bsp.dsp_version] value is the same as setting [15.A.00.069_20241211]
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.186] [function/upgrade_function.c] [read_system_version-579] : os version : 15.1.02.658_USERROOT
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.187] [common/common.c] [get_func_by_msgid-993] : unknown msgid : 00d4
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.187] [function/upgrade_function.c] [read_sdk_app_version-636] : version file : /usrdata/pilot/pilot_version.txt
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.188] [common/common.c] [wait_property_ready-1174] : waiting for property [ro.bsp.app_prepare_done] value [true], force waiting [false], timeout 40 s
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.188] [common/common.c] [wait_property_ready-1183] : get value
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.188] [common/common.c] [wait_property_ready-1218] : property [ro.bsp.app_prepare_done], aiming value [true], read value [true]
Jan 25 00:00:03 runsvdir: REGISTER_CALLBACK , callback - grayClientServer
Jan 25 00:00:03 runsvdir: grayClientServer register callback successfully, callbackHandle = 1
Jan 25 00:00:03 runsvdir: register consumer callback key: binder consumer
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.266] [function/upgrade_function.c] [read_system_version-579] : os version : 15.1.02.658_USERROOT
Jan 25 00:00:03 runsvdir: START_PREVIEW
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.267] [common/common.c] [get_func_by_msgid-993] : unknown msgid : 00d4
Jan 25 00:00:03 runsvdir: Processing message: switch to pilot 504x378 30fps
Jan 25 00:00:03 runsvdir: Switch to single stream!
Jan 25 00:00:03 runsvdir: Stream switch from 0 to 1
Jan 25 00:00:03 runsvdir: read vb config data index:3 success.
Jan 25 00:00:03 runsvdir: Create Imx681 image.
Jan 25 00:00:03 runsvdir: sensor[0] valid_flag is false, init a single stream.
Jan 25 00:00:03 runsvdir: artosyn_vi_preinit_unlocked
Jan 25 00:00:03 runsvdir: read vb config data index:3 success.
Jan 25 00:00:03 runsvdir: read vb config data index:7 success.
Jan 25 00:00:03 runsvdir: ^[[36;22mI/[25 00:00:03.270886] [Scam]: (287 gen_sensor_config)stream type<0> get vb pool id[0][1] = [3][7]^[[0m
Jan 25 00:00:03 runsvdir: artosyn_vi_preinit_unlocked sensor[0] ref_count:1
Jan 25 00:00:03 runsvdir: artosyn_vi_preinit_unlocked done
Jan 25 00:00:03 runsvdir: Processing message: start
Jan 25 00:00:03 runsvdir: Start stream
Jan 25 00:00:03 runsvdir: ^[[32;22mD/[25 00:00:03.271853] [Scam]: (537 artosyn_clock_init)AR_MPI_VIN_OpenDev^[[0m
Jan 25 00:00:03 runsvdir: name: CV0
Jan 25 00:00:03 runsvdir: sensor_type: 1
Jan 25 00:00:03 runsvdir: vi: 0
Jan 25 00:00:03 runsvdir: pipe: 1
Jan 25 00:00:03 runsvdir: channel: 2
Jan 25 00:00:03 runsvdir: i2c dev: i2c-4
Jan 25 00:00:03 runsvdir: mipi: mipi-4
Jan 25 00:00:03 runsvdir: timestamp_fifo is available, create timestamp_looper.
Jan 25 00:00:03 runsvdir: artosyn_vi_init
Jan 25 00:00:03 runsvdir: CV0timestamp_looper:open timestamp_dev /dev/imx681_exp
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.288] [function/upgrade_function.c] [read_system_version-579] : os version : 15.1.02.658_USERROOT
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.289] [common/common.c] [get_func_by_msgid-993] : unknown msgid : 00d4
Jan 25 00:00:03 runsvdir: [usb_gadget_service] DEBUG Service.cpp onTransact:129: Received string: get config
Jan 25 00:00:03 runsvdir: [usb_gadget_service] ERROR Service.cpp parseUsbGadgetConfig:79: Invalid config segment: get config
Jan 25 00:00:03 runsvdir: [usb_gadget_service] DEBUG Service.cpp onTransact:139: usb gadget no change
Jan 25 00:00:03 runsvdir: [usb_gadget_client] DEBUG nr_usb_gadget.cpp get_usb_gadget_config:234: Configuration get: ncm=enable,ecm=enable,hid=enable,uac=enable
Jan 25 00:00:03 runsvdir: ^[[36;22mI/[25 00:00:03.272647] [Scam]: (309 xr_imx681_vin_init)---start vin init--- ^[[0m
Jan 25 00:00:03 runsvdir: ^[[36;22mI/[25 00:00:03.272730] [Scam]: (310 xr_imx681_vin_init)sensor raw type:0, u8sns_mode_type:4 default_attr_index:4^[[0m
Jan 25 00:00:03 runsvdir: ^[[36;22mI/[25 00:00:03.272742] [Scam]: (312 xr_imx681_vin_init)vi_dev:0, i2c_dev:4, mipi_index:4, vi_pipe:[1 4 0 0], vi_chn:[2 2 0 0] pool_id:[3 7 -1 -1]^[[0m
Jan 25 00:00:03 runsvdir: ^[[36;22mI/[25 00:00:03.272750] [Scam]: (313 xr_imx681_vin_init)cf50 infomation: is en:0, is lossy:0, ratio:0
Jan 25 00:00:03 runsvdir: ^[[0m
Jan 25 00:00:03 runsvdir: ^[[32;22mD/[25 00:00:03.272755] [Scam]: (316 xr_imx681_vin_init)INIT-S1: XR_COMM_GetSnsObj^[[0m
Jan 25 00:00:03 runsvdir: o_O! g_sensor_info count=1
Jan 25 00:00:03 runsvdir: ^[[36;22mI/[25 00:00:03.276523] [Scam]: (91 XR_COMM_GetSnsObj)open sensor lib success, sensor name: imx681, enSnsType 4 
Jan 25 00:00:03 runsvdir: ^[[0m
Jan 25 00:00:03 runsvdir: ^[[32;22mD/[25 00:00:03.276552] [Scam]: (326 xr_imx681_vin_init)INIT-S3: XR_COMM_SYS_Init_User^[[0m
Jan 25 00:00:03 runsvdir: ^[[36;22mI/[25 00:00:03.276653] [Scam]: (151 XR_COMM_SYS_Init_User)camera vb pool is already configed.^[[0m
Jan 25 00:00:03 runsvdir: read vb config data index:3 success.
Jan 25 00:00:03 runsvdir: ^[[36;22mI/[25 00:00:03.276757] [Scam]: (195 XR_COMM_SYS_Init_User)stream type<4> get vb pool id[0][1] = [3][7]^[[0m
Jan 25 00:00:03 runsvdir: ^[[32;22mD/[25 00:00:03.276891] [Scam]: (342 xr_imx681_vin_init)INIT-S5: AR_MPI_VI_SetMipiBindDev, ViDev:0, mipi_index:4
Jan 25 00:00:03 runsvdir: ^[[0m
Jan 25 00:00:03 runsvdir: ^[[32;22mD/[25 00:00:03.276977] [Scam]: (355 xr_imx681_vin_init)INIT-S6: AR_MPI_VI_SetComboDevAttr^[[0m
Jan 25 00:00:03 runsvdir: ^[[32;22mD/[25 00:00:03.277021] [Scam]: (362 xr_imx681_vin_init)INIT-S7: AR_MPI_VI_SetDevAttr^[[0m
Jan 25 00:00:03 runsvdir: ^[[32;22mD/[25 00:00:03.277047] [Scam]: (369 xr_imx681_vin_init)INIT-S8: AR_MPI_VI_EnableDev^[[0m
Jan 25 00:00:03 runsvdir: ^[[32;22mD/[25 00:00:03.277343] [Scam]: (376 xr_imx681_vin_init)INIT-S9: --- set pipe and channel attributes ---^[[0m
Jan 25 00:00:03 runsvdir: ^[[32;22mD/[25 00:00:03.277351] [Scam]: (383 xr_imx681_vin_init)INIT-S10.1: AR_MPI_VI_SetDevBindPipe^[[0m
Jan 25 00:00:03 runsvdir: ^[[32;22mD/[25 00:00:03.277557] [Scam]: (541 xr_imx681_vin_init)INIT-S14.0: AR_MPI_VI_SetChnAttr^[[0m
Jan 25 00:00:03 runsvdir: ^[[32;22mD/[25 00:00:03.277592] [Scam]: (552 xr_imx681_vin_init)INIT-S14+1.0: AR_MPI_VI_SetChnPoolId^[[0m
Jan 25 00:00:03 runsvdir: ^[[36;22mI/[25 00:00:03.277598] [Scam]: (564 xr_imx681_vin_init)stream width[504] not align with 32 byte or is channel-2 stream, set stride as default.^[[0m
Jan 25 00:00:03 runsvdir: ^[[32;22mD/[25 00:00:03.277603] [Scam]: (568 xr_imx681_vin_init)INIT-S15.0: AR_MPI_VI_SetChnPoolId^[[0m
Jan 25 00:00:03 runsvdir: ^[[36;22mI/[25 00:00:03.277626] [Scam]: (576 xr_imx681_vin_init)vi pipe[1] channel[2] bind vb pool[3] success!^[[0m
Jan 25 00:00:03 runsvdir: ^[[32;22mD/[25 00:00:03.277658] [Scam]: (541 xr_imx681_vin_init)INIT-S14.1: AR_MPI_VI_SetChnAttr^[[0m
Jan 25 00:00:03 runsvdir: ^[[32;22mD/[25 00:00:03.277665] [Scam]: (552 xr_imx681_vin_init)INIT-S14+1.1: AR_MPI_VI_SetChnPoolId^[[0m
Jan 25 00:00:03 runsvdir: ^[[36;22mI/[25 00:00:03.277670] [Scam]: (564 xr_imx681_vin_init)stream width[258] not align with 32 byte or is channel-2 stream, set stride as default.^[[0m
Jan 25 00:00:03 runsvdir: ^[[32;22mD/[25 00:00:03.277675] [Scam]: (568 xr_imx681_vin_init)INIT-S15.1: AR_MPI_VI_SetChnPoolId^[[0m
Jan 25 00:00:03 runsvdir: ^[[36;22mI/[25 00:00:03.277688] [Scam]: (576 xr_imx681_vin_init)vi pipe[4] channel[2] bind vb pool[7] success!^[[0m
Jan 25 00:00:03 runsvdir: ^[[36;22mI/[25 00:00:03.277697] [Scam]: (619 xr_imx681_vin_init)crop X:0 Y:0 W:504 H:378 ^[[0m
Jan 25 00:00:03 runsvdir: ^[[32;22mD/[25 00:00:03.277727] [Scam]: (622 xr_imx681_vin_init)INIT-S13.0: AR_MPI_VIN_PipeBindSensor^[[0m
Jan 25 00:00:03 runsvdir: ^[[32;22mD/[25 00:00:03.278051] [Scam]: (630 xr_imx681_vin_init)INIT-S17.0: AR_MPI_ISP_MemInit^[[0m
Jan 25 00:00:03 runsvdir: ^[[32;22mD/[25 00:00:03.278083] [Scam]: (638 xr_imx681_vin_init)INIT-S18.0: AR_MPI_ISP_SetPubAttr^[[0m
Jan 25 00:00:03 runsvdir: cmos_set_wdr_mode: linear mode
Jan 25 00:00:03 runsvdir: cmos_set_image_mode:4
Jan 25 00:00:03 runsvdir: ^[[32;22mD/[25 00:00:03.278990] [Scam]: (646 xr_imx681_vin_init)INIT-S19.0: AR_MPI_VI_GetPipeExtAttr^[[0m
Jan 25 00:00:03 runsvdir: tuning file not exit, please check ...^[[32;22mD/[25 00:00:03.279085] [Scam]: (668 xr_imx681_vin_init)INIT-S20.0: AR_MPI_VI_SetPipeExtAttr^[[0m
Jan 25 00:00:03 runsvdir: ^[[32;22mD/[25 00:00:03.279127] [Scam]: (677 xr_imx681_vin_init)INIT-S21.0: AR_MPI_ISP_Init^[[0m
Jan 25 00:00:03 runsvdir: tuning file not exit, please check ...^[[32;22mD/[25 00:00:03.279175] [Scam]: (684 xr_imx681_vin_init)INIT-S22.0: AR_MPI_ISP_Run^[[0m
Jan 25 00:00:03 runsvdir: open eeprom i2c fd(29) slave addr(80) ok
Jan 25 00:00:03 runsvdir: Camera_eeprom_iic_init ok.
Jan 25 00:00:03 runsvdir: IIC read addr:0x0066 success! res:2
Jan 25 00:00:03 runsvdir: read eeprom awb awb_calibrate_valid reg data:1
Jan 25 00:00:03 runsvdir: IIC read addr:0x006f success! res:2
Jan 25 00:00:03 runsvdir: read eeprom awb CAL reg data success:
Jan 25 00:00:03 runsvdir: IIC read addr:0x0067 success! res:2
Jan 25 00:00:03 runsvdir: read eeprom awb gold reg data suc[command_service] [20270125 000003.317] [function/upgrade_function.c] [read_system_version-579] : os version : 15.1.02.658_USERROOT
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.317] [common/common.c] [get_func_by_msgid-993] : unknown msgid : 00d4
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.342] [function/upgrade_function.c] [read_system_version-579] : os version : 15.1.02.658_USERROOT
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.342] [common/common.c] [get_func_by_msgid-993] : unknown msgid : 00d4
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.343] [common/common.c] [wait_property_ready-1174] : waiting for property [sys.bsp.cam_plug_status] value [plug_in], force waiting [false], timeout 2 s
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.343] [common/common.c] [wait_property_ready-1183] : get value
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.343] [common/common.c] [wait_property_ready-1218] : property [sys.bsp.cam_plug_status], aiming value [plug_in], read value [plug_in]
Jan 25 00:00:03 runsvdir: r_lb.c: ar_aximgr_lb_enable : 285 [LB] 1920 1024 1024^[[0m
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390458][0300][0x7f80a45200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390458][0300][0x7f80a45200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390458][0300][0x7f813ae200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390458][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390458][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:00:03 runsvdir: w h change from 0 0  to 1920 1080 fetch scaler lut 
Jan 25 00:00:03 runsvdir: ^[[31;22m[124390458][0300][0x7f813ae200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:03 runsvdir: out and in is equal, use y bypass lut table 
Jan 25 00:00:03 runsvdir: 36e7d000 3709b000 37124000 0 0 0 371a7000 373c5000 3744e000 0 0 0 
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390458][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:03 runsvdir: aec index -168480335 line 1125 gain 1.000000 
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390458][0300][0x7f813ae200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390458][0300][0x7f813ae200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:03 runsvdir: w h change from 1920 1080  to 1920 1080 fetch scaler lut 
Jan 25 00:00:03 runsvdir: ^[[31;22m[124390459][0300][0x7f81616200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:03 runsvdir: out and in is equal, use y bypass lut table 
Jan 25 00:00:03 runsvdir: 374d7000 376f5000 3777e000 0 0 0 37801000 37a1f000 37aa8000 0 0 0 
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390460][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390461][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:00:03 runsvdir: ^[[33;22m[124390522][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 3 read num 3
Jan 25 00:00:03 runsvdir: ^[[0m
Jan 25 00:00:03 runsvdir: ^[[33;22m[124390522][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 471 [ahb-3] i2s out int lost 2 free num 0
Jan 25 00:00:03 runsvdir: ^[[0m
Jan 25 00:00:03 runsvdir: open the driver sns:imx681 obj:stSnsimx681Obj lib:libsns_imx681.so 
Jan 25 00:00:03 runsvdir: ^[[31;22m[25 00:00:03.277431][0313][0x7f657fa1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3554 !!!!!!!!!! Note:your app cfg the isp read and vif write  as no compress,this mode isp read and vif write will cost more dd
Jan 25 00:00:03 runsvdir: ^[[31;22m[25 00:00:03.277547][0313][0x7f657fa1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3657 stream id 0, no need cfg the sns_var_info^[[0m
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390625][0300][0x7f81f5f200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_open_camera : 920 camera_id=0^[[0m
Jan 25 00:00:03 runsvdir: ^[[33;22m[124390630][0300][0x7f813ae200][VO_CORE][WARN] display_api.c: dc_deinit : 2323 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:00:03 runsvdir: ^[[33;22m[124390630][0300][0x7f8085d200][VO_CORE][WARN] display_api.c: dc_deinit : 2323 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390630][0300][0x7f813ae200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390630][0300][0x7f813ae200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390631][0300][0x7f80a45200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_close_camera : 961 camera_id=1^[[0m
Jan 25 00:00:03 runsvdir: open the driver sns:dp_rx obj:stSnsdp_rxObj lib:libsns_dp_rx.so 
Jan 25 00:00:03 runsvdir: ^[[31;22m[25 00:00:03.353166][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3559 !!!!!!the vif is force to dpcm duce to reduce bandwidth, if you want to use the dng, please set the pstPipeAttr->enEnableN
Jan 25 00:00:03 runsvdir: ^[[31;22m[25 00:00:03.353193][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3603 !!!!!!! Note: your app cfg the vif write compress on,so you must cfg the vif fre >=3*mipi fre !!!!!!^[[0m
Jan 25 00:00:03 runsvdir: ^[[31;22m[25 00:00:03.353325][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7846 not supported addr type^[[0m
Jan 25 00:00:03 runsvdir: ^[[31;22m[[command_service] [20270125 000003.370] [function/upgrade_function.c] [read_system_version-579] : os version : 15.1.02.658_USERROOT
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.371] [common/common.c] [get_func_by_msgid-993] : unknown msgid : 00d4
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.396] [function/upgrade_function.c] [read_system_version-579] : os version : 15.1.02.658_USERROOT
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.397] [common/common.c] [get_func_by_msgid-993] : unknown msgid : 00d4
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.409] [sub_thread/usb_config_thread.c] [usb_config_thread-23] : [     ncm    ] : [hold]
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.409] [sub_thread/usb_config_thread.c] [usb_config_thread-24] : [     ecm    ] : [hold]
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.409] [sub_thread/usb_config_thread.c] [usb_config_thread-25] : [     uac    ] : [hold]
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.409] [sub_thread/usb_config_thread.c] [usb_config_thread-26] : [  hid_ctrl  ] : [hold]
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.409] [sub_thread/usb_config_thread.c] [usb_config_thread-27] : [     mtp    ] : [enable]
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.409] [sub_thread/usb_config_thread.c] [usb_config_thread-28] : [mass storage] : [hold]
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.409] [sub_thread/usb_config_thread.c] [usb_config_thread-29] : [    uvc0    ] : [hold]
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.409] [sub_thread/usb_config_thread.c] [usb_config_thread-30] : [    uvc1    ] : [hold]
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.409] [sub_thread/usb_config_thread.c] [usb_config_thread-31] : [    enable  ] : [enable]
Jan 25 00:00:03 runsvdir: [usb_gadget_service] DEBUG Service.cpp onTransact:129: Received string: mtp=enable
Jan 25 00:00:03 runsvdir: [usb_gadget_service] DEBUG Service.cpp operator():134: Executing set_usb_gadget in a separate thread...
Jan 25 00:00:03 runsvdir: [usb_gadget_service] DEBUG usb_gadget.c set_usb_gadget:370: 
Jan 25 00:00:03 runsvdir: [usb_gadget_client] DEBUG nr_usb_gadget.cpp set_usb_gadget_config:204: Configuration sent: mtp=enable
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.410] [sub_thread/usb_config_thread.c] [usb_config_thread-56] : set usb gadget config success
Jan 25 00:00:03 runsvdir: [audio service]:wait_for_poll[8]
Jan 25 00:00:03 runsvdir: [audio service]:threadObj_ai.join() timeout[5]
Jan 25 00:00:03 runsvdir: Run Umtprd service.
Jan 25 00:00:03 runsvdir: [uMTPrd - 00:00:03 - Info] uMTP Responder
Jan 25 00:00:03 runsvdir: [uMTPrd - 00:00:03 - Info] Version: v1.6.4 compiled the Sep  8 2025@16:07:22
Jan 25 00:00:03 runsvdir: [uMTPrd - 00:00:03 - Info] (c) 2018 - 2021 Viveris Technologies
Jan 25 00:00:03 runsvdir: [uMTPrd - 00:00:03 - Info] Add storage 内部存储 - Root Path: /media_data - UID: -1 - GID: -1 - Flags: 0x00000000
Jan 25 00:00:03 runsvdir: [uMTPrd - 00:00:03 - Info] USB Device path : /dev/ffs-umtp/ep0
Jan 25 00:00:03 runsvdir: [uMTPrd - 00:00:03 - Info] USB In End point path : /dev/ffs-umtp/ep1
Jan 25 00:00:03 runsvdir: [uMTPrd - 00:00:03 - Info] USB Out End point path : /dev/ffs-umtp/ep2
Jan 25 00:00:03 runsvdir: [uMTPrd - 00:00:03 - Info] USB Event End point path : /dev/ffs-umtp/ep3
Jan 25 00:00:03 runsvdir: [uMTPrd - 00:00:03 - Info] USB Max packet size : 0x200 bytes
Jan 25 00:00:03 runsvdir: [uMTPrd - 00:00:03 - Info] USB Max write buffer size : 0x2000 bytes
Jan 25 00:00:03 runsvdir: [uMTPrd - 00:00:03 - Info] USB Max read buffer size : 0x2000 bytes
Jan 25 00:00:03 runsvdir: [uMTPrd - 00:00:03 - Info] Read file buffer size : 0x100000 bytes
Jan 25 00:00:03 runsvdir: [uMTPrd - 00:00:03 - Info] Manufacturer string : XREAL
Jan 25 00:00:03 runsvdir: [uMTPrd - 00:00:03 - Info] Product string : XREAL One
Jan 25 00:00:03 runsvdir: [uMTPrd - 00:00:03 - Info] Serial string : 578A62E733D8E83AE172F0B87A3F02C9
Jan 25 00:00:03 runsvdir: [uMTPrd - 00:00:03 - Info] Firmware Version string : Rev A
Jan 25 00:00:03 runsvdir: [uMTPrd - 00:00:03 - Info] Interface string : MTP
Jan 25 00:00:03 runsvdir: [uMTPrd - 00:00:03 - Info] USB Vendor ID : 0x3318
Jan 25 00:00:03 runsvdir: [uMTPrd - 00:00:03 - Info] USB Product ID : 0x0438
Jan 25 00:00:03 runsvdir: [uMTPrd - 00:00:03 - Info] USB class ID : 0x06
Jan 25 00:00:03 runsvdir: [uMTPrd - 00:00:03 - Info] USB subclass ID : 0x01
Jan 25 00:00:03 runsvdir: [uMTPrd - 00:00:03 - Info] USB Protocol ID : 0x01
Jan 25 00:00:03 runsvdir: [uMTPrd - 00:00:03 - Info] USB Device version : 0x3008
Jan 25 00:00:03 runsvdir: [uMTPrd - 00:00:03 - Info] USB FunctionFS Mode
Jan 25 00:00:03 runsvdir: [uMTPrd - 00:00:03 - Info] Wait for connection : 0
Jan 25 00:00:03 runsvdir: [uMTPrd - 00:00:03 - Info] Loop on disconnect : 1
Jan 25 00:00:03 runsvdir: [uMTPrd - 00:00:03 - Info] Show hidden files : 1
Jan 25 00:00:03 runsvdir: [uMTPrd - 00:00:03 - Info] File creation umask : System default umask
Jan 25 00:00:03 runsvdir: [uMTPrd - 00:00:03 - Info] Default UID : 0
Jan 25 00:00:03 runsvdir: [uMTPrd - 00:00:03 - Info] Default GID : 0
Jan 25 00:00:03 runsvdir: [uMTPrd - 00:00:03 - Info] inotify : yes
Jan 25 00:00:03 runsvdir: exfatprogs version : 1.2.5
Jan 25 00:00:03 runsvdir: /dev/mmcblk0p22: clean. directories 1, files 0
Jan 25 00:00:03 runsvdir: [usb_gadget_service] ERROR usb_gadget.c set_usb_gadget:529: Created symlink: /sys/kernel/config/usb_gadget/g0/configs/b.1/f_mtp -> /sys/kernel/config/usb_gadget/g0/functions/ffs.mtp
Jan 25 00:00:03 runsvdir: 25 00:00:03.353335][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7856 not supported data type^[[0m
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390632][0300][0x7f812a9200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_open_camera : 920 camera_id=1^[[0m
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390633][0300][0x7f8083c200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=0^[[0m
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390633][0300][0x7f812a9200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390633][0300][0x7f812a9200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390633][0300][0x7f812a9200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390633][0300][0x7f8083c200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390633][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390633][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390633][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:03 runsvdir: w h change from 0 0  to 1920 1080 fetch scaler lut 
Jan 25 00:00:03 runsvdir: ^[[31;22m[124390633][0300][0x7f812a9200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:03 runsvdir: out and in is equal, use y bypass lut table 
Jan 25 00:00:03 runsvdir: 36e7d000 3709b000 37124000 0 0 0 371a7000 373c5000 3744e000 0 0 0 
Jan 25 00:00:03 runsvdir: aec index 1399374359 line 1125 gain 1.000000 
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390633][0300][0x7f812a9200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390633][0300][0x7f812a9200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390633][0300][0x7f8083c200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=0 stream_id=0^[[0m
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390633][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4384 power on vc 0 0^[[0m
Jan 25 00:00:03 runsvdir: 374d7000 376f5000 3777e000 0 0 0 37801000 37a1f000 37aa8000 0 0 0 
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390635][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390636][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390636][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4386 power on vc end 0^[[0m
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390643][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390643][0300][0x7f8083c200][SYS_CORE][VI_KEY] mipi_rx_9411.c: mipi_rx_power_up_9411 : 64 enable the mipi cfg and pcs clock, and set pcs to clk=100000000hz^[[0m
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390643][0300][0x7f8083c200][SYS_CORE][VI_KEY] mipi_rx.c: one_mipi_init : 1204 mipi 4 init done^[[0m
Jan 25 00:00:03 runsvdir: aec index 198685398 line 7042 gain 1.000000 
Jan 25 00:00:03 runsvdir:  imx681_stream_on
Jan 25 00:00:03 runsvdir:  imx681_trigger_on
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390644][0300][0x7f8083c200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390644][0300][0x7f8083c200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=0^[[0m
Jan 25 00:00:03 runsvdir: ^[[31;22m[124390644][0300][0x7f8083c200][VI_CORE][ERROR] rx_filter_port.c: rx_filter_get_link_res : 304 pick stream err^[[0m
Jan 25 00:00:03 runsvdir: ^[[31;22m[124390644][0300][0x7f8083c200][VI_CORE][ERROR] vif_in.c: vif_inlib_set_ctl : 304 the vif input lib [16] is already used by other vc channel^[[0m
Jan 25 00:00:03 runsvdir: ^[[31;22m[124390644][0300][0x7f8083c200][VI_CORE][ERROR] stream.c: link_filter_external : 407 check_linking_stream error:-2146402228^[[0m
Jan 25 00:00:03 runsvdir: ^[[31;22m[124390644][0300][0x7f8083c200][VI_CORE][ERROR] rx_filter_port.c: rx_filter_get_link_res : 304 pick stream err^[[0m
Jan 25 00:00:03 runsvdir: ^[[31;22m[124390644][0300][0x7f8083c200][VI_CORE][ERROR] vif_in.c: vif_inlib_set_ctl : 304 the vif input lib [16] is already used by other vc channel^[[0m
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390644][0300][0x7f8083c200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=1^[[0m
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390644][0300][0x7f82382200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=0 stcess:
Jan 25 00:00:03 runsvdir: IIC read addr:0x009c success! res:2
Jan 25 00:00:03 runsvdir: read eeprom lsc_calibrate_valid reg data:1
Jan 25 00:00:03 runsvdir: DisplayService I : get dp info 1920x1080@90
Jan 25 00:00:03 runsvdir: DisplayService I : get a cmd: stop dpisp
Jan 25 00:00:03 runsvdir: DisplayService I : dpisp get a new state:0
Jan 25 00:00:03 runsvdir: DisplayService I : <timestamp DPSIP stop start> -- [ 3.769 ]
Jan 25 00:00:03 runsvdir: DisplayService I : <timestamp force release vb done> -- [ 3.769 ]
Jan 25 00:00:03 runsvdir: IIC read addr:0x03bd success! res:2
Jan 25 00:00:03 runsvdir: read eeprom lsc calibrate reg len:2, data:
Jan 25 00:00:03 runsvdir: DisplayService I : free vb count = 8, inused vb count = 0
Jan 25 00:00:03 runsvdir: DisplayService I : <timestamp DPSIP stop done> -- [ 3.776 ]
Jan 25 00:00:03 runsvdir: DisplayService D : destory pipi = 2, isp object = 0x7f91f88108
Jan 25 00:00:03 runsvdir: DisplayService I : <timestamp destory vi done> -- [ 3.777 ]
Jan 25 00:00:03 runsvdir: DisplayService I : current dpisp status update :0
Jan 25 00:00:03 runsvdir: DisplayOLED I : screen soft in change refresh reset successfully!
Jan 25 00:00:03 runsvdir: DisplayOLED I : OLED no set force lane!
Jan 25 00:00:03 runsvdir: DisplayOLED I : GF screen set inverse scan direction
Jan 25 00:00:03 runsvdir: DisplayService I : Configure OLED to 5lanesX2@90Hz Success
Jan 25 00:00:03 runsvdir: DisplayService W : can't find node 0x7f8c001dd0, ignore this release
Jan 25 00:00:03 runsvdir: DisplayService I : get cmd CMD_VI_RESUME_BY_VO
Jan 25 00:00:03 runsvdir: DisplayService I : dpisp get a new state:1
Jan 25 00:00:03 runsvdir: DisplayService D : get dprx so
Jan 25 00:00:03 runsvdir: DisplayService D : init pipi = 2, isp object = 0x7f91f88108
Jan 25 00:00:03 runsvdir: DisplayService I : enPixelFormat:0x17
Jan 25 00:00:03 runsvdir: DisplayService I : get dpvif_fre_hz = 333000000
Jan 25 00:00:03 runsvdir: DisplayService I : >>>>>> stComboAttr >>>>>>>
Jan 25 00:00:03 runsvdir: DisplayService I : devno: 0
Jan 25 00:00:03 runsvdir: DisplayService I : input_mode: 0x0b
Jan 25 00:00:03 runsvdir: DisplayService I : data_rate : 450
Jan 25 00:00:03 runsvdir: DisplayService I : mipi-input_data_type: 0x02
Jan 25 00:00:03 runsvdir: DisplayService I : stSnsSize.u32Width = 1920
Jan 25 00:00:03 runsvdir: DisplayService I : stSnsSize.u32Height = 1080
Jan 25 00:00:03 runsvdir: DisplayService I : stTiming.hblank = 280
Jan 25 00:00:03 runsvdir: DisplayService I : stTiming.vblank = 45
Jan 25 00:00:03 runsvdir: DisplayService I : stChnAttr.stSize.u32Width = 1920
Jan 25 00:00:03 runsvdir: DisplayService I : stChnAttr.stSize.u32Height = 1080
Jan 25 00:00:03 runsvdir: DisplayService I : stChnAttr.u32BufCount = 8
Jan 25 00:00:03 runsvdir: DisplayService I : stPubAttr.f32FrameRate = 0.000000
Jan 25 00:00:03 runsvdir: DisplayService I : <<<<<< stComboAttr <<<<<<<
Jan 25 00:00:03 runsvdir: IIC read addr:0x009d success! res:2
Jan 25 00:00:03 runsvdir: read eeprom lsc gold reg len:2, data:
Jan 25 00:00:03 runsvdir: Close camera-0 i2c fd sucess.
Jan 25 00:00:03 runsvdir: Camera eeprom iic release ok.
Jan 25 00:00:03 runsvdir: read vb config data index:4 success.
Jan 25 00:00:03 runsvdir: DisplayService I : pipe:2 channel:0 get vb pool id <4>!
Jan 25 00:00:03 runsvdir: read vb config data index:4 success.
Jan 25 00:00:03 runsvdir: get vb pool id <4>!
Jan 25 00:00:03 runsvdir: DisplayService I : pipe:2 chn:0 bind vb pool:4 success!
Jan 25 00:00:03 runsvdir: DisplayService I : <timestamp AR_MPI_VI_GetHightPricisionPipeFPS start> -- [ 3.800 ]
Jan 25 00:00:03 runsvdir: IMX681: bInit[0] enWDRMode[0] u8ImgMode[4]
Jan 25 00:00:03 runsvdir: u8DevNum=4, /dev/i2c-4
Jan 25 00:00:03 runsvdir: ==============================================================
Jan 25 00:00:03 runsvdir: == SENSOR_SINGLE_STREAM_QVGA_504x378_30FPS_MODE 30fps inital ==
Jan 25 00:00:03 runsvdir: ==============================================================
Jan 25 00:00:03 runsvdir: imx681_cfg_ebd 256*2 
Jan 25 00:00:03 runsvdir: ^[[32;22mD/[25 00:00:03.464366] [Scam]: (692 xr_imx681_vin_init)INIT-S23.0: trigger AEC AWB^[[0m
Jan 25 00:00:03 runsvdir: ^[[36;22mI/[25 00:00:03.464402] [Scam]: (619 xr_imx681_vin_init)crop X:0 Y:0 W:258 H:2 ^[[0m
Jan 25 00:00:03 runsvdir: ^[[32;22mD/[25 00:00:03.464417] [Scam]: (622 xr_imx681_vin_init)INIT-S13.1: AR_MPI_VIN_PipeBindSensor^[[0m
Jan 25 00:00:03 runsvdir: ^[[32;22mD/[25 00:00:03.464455] [Scam]: (630 xr_imx681_vin_init)INIT-S17.1: AR_MPI_ISP_MemInit^[[0m
Jan 25 00:00:03 runsvdir: ^[[32;22mD/[25 00:00:03.464467] [Scam]: (638 xr_imx681_vin_init)INIT-S18.1: AR_MPI_ISP_SetPubAttr^[[0m
Jan 25 00:00:03 runsvdir: cmos_set_wdr_mode: linear mode
Jan 25 00:00:03 runsvdir: cmos_set_image_mode:4
Jan 25 00:00:03 runsvdir: ^[[32;22mD/[25 00:00:03.464989] [Scam]: (646 xr_imx681_vin_init)INIT-S19.1: AR_MPI_VI_GetPipeExtAttr^[[0m
Jan 25 00:00:03 runsvdir: tuning file not exit, please check ...^[[32;22mD/[25 00:00:03.465084] [Scam]: (668 xr_imx681_vin_init)INIT-S20.1: AR_MPI_VI_SetPipeExtAttr^[[0m
Jan 25 00:00:03 runsvdir: ^[[32;22mD/[25 00:00:03.465095] [Scam]: (677 xr_imx681_vin_init)INIT-S21.1: AR_MPI_ISP_Init^[[0m
Jan 25 00:00:03 runsvdir: tuning file not exit, please check ...^[[32;22mD/[25 00:00:03.465119] [Scam]: (684 xr_imx681_vin_init)INIT-S22.1: AR_MPI_ISP_Run^[[0m
Jan 25 00:00:03 runsvdir: ^[[32;22mD/[25 00:00:03.472098] [Scam]: (692 xr_imx681_vin_init)INIT-S23.1: trigger AEC AWB^[[0m
Jan 25 00:00:03 runsvdir: xr_imx681_vin_init success
Jan 25 00:00:03 runsvdir: artosyn_vi_init done
Jan 25 00:00:03 runsvdir: get_sensor_ebd_datafifo sensor_index:0
Jan 25 00:00:03 runsvdir: ebd_fifo aquire:ref_count = 1
Jan 25 00:00:03 runsvdir: ebd_info_fifo is available, create ebd_thread.
Jan 25 00:00:03 runsvdir: Current line_exp_ns:4733
Jan 25 00:00:03 runsvdir: Set auto exp.
Jan 25 00:00:03 runsvdir: ^[[31;22mE/[25 00:00:03.473095] [Scam]: (1614 artosyn_isp_set_autoexp)set exposure_type failed!(-1)
Jan 25 00:00:03 runsvdir: ^[[0m
Jan 25 00:00:03 runsvdir: Start working thread
Jan 25 00:00:03 runsvdir: DisplayService I : get high pricision fps = 90.003136
Jan 25 00:00:03 runsvdir: DisplayService I : <timestamp AR_MPI_VI_GetHightPricisionPipeFPS end> -- [ 3.960 ]
Jan 25 00:00:03 runsvdir: DisplayService I : isp attr: type : bt709, color range : full range
Jan 25 00:00:03 runsvdir: DisplayService I : <timestamp init vi done> -- [ 3.975 ]
Jan 25 00:00:03 runsvdir: DisplayService I : current dpisp status update :1
Jan 25 00:00:03 runsvdir: CV stream Processor queue is empty!
Jan 25 00:00:03 runsvdir: CV stream Consumer queue is empty!
Jan 25 00:00:03 runsvdir: [storage_service] DEBUG storage.c get_usb_storage_available:760: :0
Jan 25 00:00:03 runsvdir: DisplayService I : get a cmd: start dpisp
Jan 25 00:00:03 runsvdir: DisplayService I : dpisp get a new state:2
Jan 25 00:00:03 runsvdir: DisplayService I : only enable dpisp
Jan 25 00:00:03 runsvdir: DisplayService I : <timestamp init isp > -- [ 3.999 ]
Jan 25 00:00:03 runsvdir: DisplayService I : enable vi channel output
Jan 25 00:00:03 runsvdir: DisplayService I : check oled status:close, ret = 0
Jan 25 00:00:03 runsvdir: DisplayService I : set duty 0
Jan 25 00:00:03 runsvdir: DisplayService I : Set original LUT...
Jan 25 00:00:03 runsvdir: DisplayService I : <timestamp init isp done> -- [ 4.9 ]
Jan 25 00:00:03 runsvdir: DisplayService I : current dpisp status update :2
Jan 25 00:00:03 runsvdir: CV stream Freelist queue is empty!
Jan 25 00:00:03 runsvdir: [storage_service] DEBUG storage.c get_total_size:386: service: total_size:2145910784
Jan 25 00:00:03 runsvdir: [storage_service] DEBUG storage.c get_available_size:425: service: available_size:2145746944
Jan 25 00:00:03 runsvdir: ream_id=1^[[0m
Jan 25 00:00:03 runsvdir: aec index 198685398 line 7042 gain 1.000000 
Jan 25 00:00:03 runsvdir:  imx681_stream_on
Jan 25 00:00:03 runsvdir:  imx681_trigger_on
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390644][0300][0x7f82382200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:03 runsvdir: ^[[31;22m[25 00:00:03.473004][0313][0x7f657fa1d0][VI_HAL][ERROR] ar_hal_vin.c: ar_hal_vin_get_3a_info : 3625 Invalid fd -1
Jan 25 00:00:03 runsvdir: ^[[0m
Jan 25 00:00:03 runsvdir: ^[[31;22m[25 00:00:03.473084][0313][0x7f657fa1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetAecManuTidyAttr : 12733 isp not runing, can not set prop^[[0m
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390649][0300][0x7f82382200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390650][0300][0x7f82382200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390650][0300][0x7f81616200][SYS_CORE][VI_KEY] vif_mid.c: vif_bpintr_process : 1125 stable ,ento ddr view 0^[[0m
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390651][0300][0x7f81616200][SYS_CORE][VI_KEY] vif_mid.c: vif_bpintr_process : 1125 stable ,ento ddr view 2^[[0m
Jan 25 00:00:03 runsvdir: ^[[31;22m[124390652][0498][0x7f9a960010][VPSS_MPP][ERROR] mpi_vpss.c: ar_hal_vpss_create : 870 bind_register_sender failed 0x80028003^[[0m
Jan 25 00:00:03 runsvdir: ^[[33;22m[124390653][0300][0x7f82382200][VO_CORE][WARN] display_api.c: dc_init : 2258 use dc_reg_irq_by_cmd!^[[0m
Jan 25 00:00:03 runsvdir: ^[[33;22m[124390653][0300][0x7f82382200][SYS_CORE][WARN] ar_subvision_ctrl.c: ar_subvision_vsync_statistic_disable : 483 subvision already disable!^[[0m
Jan 25 00:00:03 runsvdir: ^[[31;22m[124390653][0300][0x7f82382200][VO_CORE][ERROR] display_top.c: ar_display_dev_enable : 3521 not support set irq_type:^[[33;22m[124390653][0300][0x7f8083c200][VO_CORE][WARN] display_api.c: dc_init : 2258 use dc_reg_irq_
Jan 25 00:00:03 runsvdir: ^[[33;22m[124390653][0300][0x7f8083c200][SYS_CORE][WARN] ar_subvision_ctrl.c: ar_subvision_vsync_statistic_disable : 483 subvision already disable!^[[0m
Jan 25 00:00:03 runsvdir: ^[[33;22m[124390653][0300][0x7f82382200][AXIMGR_CORE][WARN] ar_aximgr_lb.c: ar_aximgr_lb_enable : 285 [LB] 1920 1024 1024^[[0m
Jan 25 00:00:03 runsvdir: ^[[33;22m[124390653][0300][0x7f8083c200][AXIMGR_CORE][WARN] ar_aximgr_lb.c: ar_aximgr_lb_enable : 285 [LB] 1920 1024 1024^[[0m
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390653][0300][0x7f808c0200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390654][0300][0x7f808c0200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390654][0300][0x7f81267200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390654][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:00:03 runsvdir: w h change from 0 0  to 1920 1080 fetch scaler lut 
Jan 25 00:00:03 runsvdir: ^[[31;22m[124390654][0300][0x7f81267200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:03 runsvdir: out and in is equal, use y bypass lut table 
Jan 25 00:00:03 runsvdir: 36e7d000 3709b000 37124000 0 0 0 371a7000 373c5000 3744e000 0 0 0 
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390654][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390654][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:03 runsvdir: aec index 198685398 line 1125 gain 1.000000 
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390654][0300][0x7f81267200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390654][0300][0x7f81267200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:03 runsvdir: w h change from 1920 1080  to 1920 1080 fetch scaler lut 
Jan 25 00:00:03 runsvdir: ^[[31;22m[124390655][0300][0x7f81616200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:03 runsvdir: out and in is equal, use y bypass lut table 
Jan 25 00:00:03 runsvdir: 374d7000 376f5000 3777e000 0 0 0 37801000 37a1f000 37aa8000 0 0 0 
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390656][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:00:03 runsvdir: ^[[33;22m[124390656][0300][0x7f809a5200][SYS_CORE][WARN] ar_subvision_ctrl.c: ar_subvision_vsync_statistic_enable : 456 subvision already enable!^[[0m
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390657][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:00:03 runsvdir: ^[[35;22m[124390657][0300][0x7f81616200][SYS_CORE][VI_KEY] vif_mid.c: vif_bpintr_process : 117[audio service]:[audio]:handleClientRequest cmd[46]
Jan 25 00:00:03 runsvdir: [audio service]:[audio binder service]:GET_SDK_MUTE_STATUS sdk_mute[1]
Jan 25 00:00:03 runsvdir: [audio service]:[audio]:handleClientRequest cmd[47]
Jan 25 00:00:03 runsvdir: [audio service]:[audio binder service]:SET_SDK_MUTE_STATUS sdk_mute[0]
Jan 25 00:00:03 runsvdir: [audio service]:[audio]:handleClientRequest cmd[46]
Jan 25 00:00:03 runsvdir: [audio service]:[audio binder service]:GET_SDK_MUTE_STATUS sdk_mute[0]
Jan 25 00:00:03 runsvdir: [audio service]:UVC_AUDIO_Ao snd no dev
Jan 25 00:00:03 runsvdir: [audio service]:dsp_ctrl_thread:will exit
Jan 25 00:00:03 runsvdir: [audio service]:dsp_ctrl_thread:exit
Jan 25 00:00:03 runsvdir: [audio service]:snd_ctl_volume_thread exit!
Jan 25 00:00:03 runsvdir: [audio service]:latency=10666
Jan 25 00:00:03 runsvdir: [audio service]:UVC_AUDIO_Ao run
Jan 25 00:00:03 runsvdir: [audio service]:snd_ctl_volume_thread[0] run 
Jan 25 00:00:03 runsvdir: [audio service]:HW info suc: 0
Jan 25 00:00:03 runsvdir: [audio service]:  card - 0
Jan 25 00:00:03 runsvdir: [audio service]:  id - 'UAC1Gadget'
Jan 25 00:00:03 runsvdir: [audio service]:  driver - 'UAC1_Gadget'
Jan 25 00:00:03 runsvdir: [audio service]:  name - 'UAC1_Gadget'
Jan 25 00:00:03 runsvdir: [audio service]:  longname - 'UAC1_Gadget 0'
Jan 25 00:00:03 runsvdir: [audio service]:  mixername - 'UAC1_Gadget'
Jan 25 00:00:03 runsvdir: [audio service]:  components - ''
Jan 25 00:00:03 runsvdir: [audio service]:elem list count=7
Jan 25 00:00:03 runsvdir: [audio service]:numid=1, name='Playback Pitch 1000000'
Jan 25 00:00:03 runsvdir: [audio service]:numid=2, name='PCM Playback Switch'
Jan 25 00:00:03 runsvdir: [audio service]:numid=3, name='PCM Playback Volume'
Jan 25 00:00:03 runsvdir: [audio service]:numid=4, name='Playback Rate'
Jan 25 00:00:03 runsvdir: [audio service]:numid=5, name='PCM Capture Switch'
Jan 25 00:00:03 runsvdir: [audio service]:numid=6, name='PCM Capture Volume'
Jan 25 00:00:03 runsvdir: [audio service]:numid=7, name='Capture Rate'
Jan 25 00:00:03 runsvdir: [audio service]:snd_numid_handle :5 1
Jan 25 00:00:03 runsvdir: [audio service]:smartPA start su
Jan 25 00:00:03 runsvdir: [audio service]:snd_numid_handle :7 0
Jan 25 00:00:03 runsvdir: [audio service]:snd_numid_handle :6 0
Jan 25 00:00:03 runsvdir: [audio service]:hsv[0],index[0]dspv[1000]
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.617] [hardware/DSP_IA8201/src/dsp_mode_set.c] [route_set_volume-539] : setting volume 1000, register value 000be800
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.619] [hardware/DSP_IA8201/src/dsp_mode_set.c] [route_set_volume-552] : set volume [1000], return [0]
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.670] [hardware/DSP_IA8201/src/dsp_mode_set.c] [route_read_volume-560] : try lock dsp
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.670] [hardware/DSP_IA8201/src/dsp_mode_set.c] [route_read_volume-582] : read volume [1000], return [0]
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.670] [sub_thread/communicate_to_audio_service.c] [handle_audio_service_msg-191] : get dsp volume back over : 1000
Jan 25 00:00:03 runsvdir: [audio service]:read_dsp_characteristic reg[e8][3] length[2]
Jan 25 00:00:03 runsvdir: [audio service]:set_dsp_volume result[0] volume[1000]
Jan 25 00:00:03 runsvdir: [audio service]:volume changing , volume is:1000 0
Jan 25 00:00:03 runsvdir: [audio service]:snd_numid_handle :4 0
Jan 25 00:00:03 runsvdir: [audio service]:dsp_mode_without_mic :1
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.682] [command_service.c] [main-834] : hid fd is invalid, restart command_service
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.682] [command_service.c] [main-843] : ota service end
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.720] [hardware/DSP_IA8201/src/dsp_mode_set.c] [Route_SetMode-472] : try lock dsp
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.732] [hardware/DSP_IA8201/src/dsp_mode_set.c] [Route_SetMode-509] : set mode [1], return [0]
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.733] [sub_thread/communicate_to_audio_service.c] [handle_audio_service_msg-126] : set dsp mode back over : 0
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.772] [command_service.c] [stop_thread_all-262] : No.0 hid_data_proces thread exit
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.772] [command_service.c] [delete_task-218] : primary 0x49d630
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.772] [command_service.c] [delete_task-221] : cur 0x49d630, cur->tid : 548286755296, tid : 548286755296, next 0x49d7e0
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.772] [command_service.c] [delete_thread_node-106] : cur : (nil), next 0x49d7e0, head : 0x49d7e0
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.772] [command_service.c] [stop_thread_all-262] : No.1 tcp_data_proces thread exit
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.772] [command_service.c] [delete_task-218] : primary 0x49d7e0
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.772] [command_service.c] [delete_task-221] : cur 0x49d7e0, cur->tid : 548286620128, tid : 548286620128, next 0x49d990
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.772] [command_service.c] [delete_thread_node-106] : cur : (nil), next 0x49d990, head : 0x49d990
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.773] [command_service.c] [stop_thread_all-262] : No.2 hid_data_receiv thread exit
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.773] [command_service.c] [delete_task-218] : primary 0x49d990
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.773] [command_service.c] [delete_task-221] : cur 0x49d990, cur->tid : 548286484960, tid : 548286484960, next 0x49db40
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.773] [command_service.c] [delete_thread_node-106] : cur : (nil), next 0x49db40, head : 0x49db40
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.773] [command_service.c] [stop_thread_all-262] : No.3 hid_data_send thread exit
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.773] [command_service.c] [delete_task-218] : primary 0x49db40
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.773] [command_service.c] [delete_task-221] : cur 0x49db40, cur->tid : 548286349792, tid : 548286349792, next 0x49dcf0
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.773] [command_service.c] [delete_thread_node-106] : cur : (nil), next 0x49dcf0, head : 0x49dcf0
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.773] [command_service.c] [stop_thread_all-262] : No.4 restart_control thread exit
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.773] [command_service.c] [delete_task-218] : primary 0x49dcf0
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.773] [command_service.c] [delete_task-221] : cur 0x49dcf0, cur->tid : 548286214624, tid : 548286214624, next 0x49dea0
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.773] [command_service.c] [delete_thread_node-106] : cur : (nil), next 0x49dea0, head : 0x49dea0
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.773] [command_service.c] [stop_thread_all-262] : No.5 tcp_data_receiv thread exit
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.773] [command_service.c] [delete_task-218] : primary 0x49dea0
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.773] [command_service.c] [delete_task-221] : cur 0x49dea0, cur->tid : 548286079456, tid : 548286079456, next 0x49e050
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.773] [command_service.c] [delete_thread_node-106] : cur : (nil), next 0x49e050, head : 0x49e050
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.773] [command_service.c] [stop_thread_all-262] : No.6 tcp_data_send thread exit
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.773] [command_service.c] [delete_task-218] : primary 0x49e050
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.773] [command_service.c] [delete_task-221] : cur 0x49e050, cur->tid : 548285944288, tid : 548285944288, next 0x49e200
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.773] [command_service.c] [delete_thread_node-106] : cur : (nil), next 0x49e200, head : 0x49e200
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.774] [command_service.c] [stop_thread_all-262] : No.7 dsp_upgrade thread exit
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.774] [command_service.c] [delete_task-218] : primary 0x49e200
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.774] [command_service.c] [delete_task-221] : cur 0x49e200, cur->tid : 548285809120, tid : 548285809120, next 0x49e3b0
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.774] [command_service.c] [delete_thread_node-106] : cur : (nil), next 0x49e3b0, head : 0x49e3b0
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.775] [command_service.c] [stop_thread_all-262] : No.8 upgrade_monitor thread exit
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.775] [command_service.c] [delete_task-218] : primary 0x49e3b0
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.775] [command_service.c] [delete_task-221] : cur 0x49e3b0, cur->tid : 548285673952, tid : 548285673952, next 0x49e560
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.775] [command_service.c] [delete_thread_node-106] : cur : (nil), next 0x49e560, head : 0x49e560
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.775] [command_service.c] [stop_thread_all-262] : No.9 usb_config thread exit
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.775] [command_service.c] [delete_task-218] : primary 0x49e560
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.775] [command_service.c] [delete_task-221] : cur 0x49e560, cur->tid : 548285538784, tid : 548285538784, next 0x49e750
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.775] [command_service.c] [delete_thread_node-106] : cur : (nil), next 0x49e750, head : 0x49e750
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.775] [command_service.c] [stop_thread_all-262] : No.10 cmd_audio thread exit
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.775] [command_service.c] [delete_task-218] : primary 0x49e750
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.775] [command_service.c] [delete_task-221] : cur 0x49e750, cur->tid : 548285403616, tid : 548285403616, next (nil)
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.775] [command_service.c] [delete_thread_node-106] : cur : (nil), next (nil), head : (nil)
Jan 25 00:00:03 runsvdir: Command_service exit, recycling resource.
Jan 25 00:00:03 runsvdir: [audio service]:open pipe file failed -1 -1 No such device or address
Jan 25 00:00:03 runsvdir: Run Command service.
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.797] [upgrade/upgrade.c] [init_upgrade_function-12] : media type : emmc
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.797] [upgrade/upgrade.c] [init_upgrade_function-13] : adjust emmc : true
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.798] [upgrade/upgrade.c] [init_upgrade_function-14] : adjust nand : false
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.798] [upgrade/upgrade.c] [init_upgrade_function-16] : emmc init
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.804] [upgrade/emmc_upgrade.c] [get_mmc_start_part-489] : start from gpt : 0
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.805] [upgrade/emmc_upgrade.c] [get_mmc_param_start_part-522] : root number : 19
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.805] [command_service.c] [check_ipc_file-428] : file exist
Jan 25 00:00:03 runsvdir: [usb_gadget_service] DEBUG Service.cpp onTransact:129: Received string: get config
Jan 25 00:00:03 runsvdir: [usb_gadget_service] ERROR Service.cpp parseUsbGadgetConfig:79: Invalid config segment: get config
Jan 25 00:00:03 runsvdir: [usb_gadget_service] DEBUG Service.cpp onTransact:139: usb gadget no change
Jan 25 00:00:03 runsvdir: [usb_gadget_client] DEBUG nr_usb_gadget.cpp get_usb_gadget_config:234: Configuration get: ncm=enable,ecm=enable,mtp=enable,hid=enable,uac=enable
Jan 25 00:00:03 runsvdir: [audio service]:open pipe file failed -1 -1 No such device or address
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.849] [common/common.c] [get_process_state-1089] : process dhcpd status : S
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.849] [common/common.c] [start_process_by_name-650] : process dhcpd is running
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.850] [command_service.c] [create_task-199] : prepare to create thread : 0, name : hid_data_process
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.850] [command_service.c] [create_task-199] : prepare to create thread : 1, name : tcp_data_process
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.850] [command_service.c] [create_task-199] : prepare to create thread : 2, name : hid_data_receive
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.850] [command_service.c] [create_task-199] : prepare to create thread : 3, name : hid_data_send
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.850] [sub_thread/hid_receive_send_thread.c] [hid_data_receive_thread-118] : start
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.851] [command_service.c] [create_task-199] : prepare to create thread : 4, name : restart_control
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.851] [command_service.c] [create_task-199] : prepare to create thread : 5, name : tcp_data_receive
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.851] [command_service.c] [create_task-199] : prepare to create thread : 6, name : tcp_data_send
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.851] [sub_thread/tcp_receive_send_thread.c] [tcp_data_receive_thread-26] : waiting connection...
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.851] [command_service.c] [create_task-199] : prepare to create thread : 7, name : dsp_upgrade
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.851] [command_service.c] [create_task-199] : prepare to create thread : 8, name : upgrade_monitor
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.852] [command_service.c] [create_task-199] : prepare to create thread : 9, name : usb_config
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.852] [hardware/DSP_IA8201/src/dsp_io_ctl.c] [dsp_io_init-23] : ret : 0, mode : 1
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.852] [hardware/DSP_IA8201/src/uart.c] [get_uart_tty_path-30] : tty path : /sys/devices/platform/soc/1504000.serial/tty
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.852] [hardware/DSP_IA8201/src/uart.c] [get_uart_tty_path-37] : .
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.852] [hardware/DSP_IA8201/src/uart.c] [get_uart_tty_path-37] : ..
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.852] [hardware/DSP_IA8201/src/uart.c] [get_uart_tty_path-37] : ttyS2
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.852] [hardware/DSP_IA8201/src/uart.c] [get_uart_tty_path-42] : name : ttyS2
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.852] [hardware/DSP_IA8201/src/uart.c] [get_uart_tty_path-46] : get uart node : /dev/ttyS2
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.857] [hardware/DSP_IA8201/src/uart.c] [get_uart_tty_path-54] : serial irq : 32
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.860] [hardware/DSP_IA8201/src/dsp_mode_set.c] [get_dsp_sysmode-810] : read mode : 2
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.860] [command_service.c] [create_task-199] : prepare to create thread : 10, name : cmd_audio
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.861] [function/upgrade_function.c] [read_system_version-579] : os version : 15.1.02.658_USERROOT
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.861] [command_service.c] [set_property_values-724] : version code str : 2658, version code : 2658
Jan 25 00:00:03 runsvdir: libc: Unable to set property "ro.bsp.system_version_code" to "2658": error code: 0xb
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.862] [command_service.c] [set_property_values-728] : set property system version code failed
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.862] [function/upgrade_function.c] [read_dsp_version-1046] : lock dsp
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.863] [function/upgrade_function.c] [read_dsp_version-1052] : dsp version : 15.A.00.069_20241211
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.863] [common/common.c] [update_property_file-1314] : key offset [0], value offset [20] eof offset [41]
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.863] [common/common.c] [update_property_file-1338] : file [/persist/device.prop] key [sys.bsp.dsp_version] value is the same as setting [15.A.00.069_20241211]
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.864] [common/common.c] [update_property_file-1314] : key offset [41], value offset [60] eof offset [81]
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.864] [common/common.c] [update_property_file-1338] : file [/persist/device.prop] key [ro.bsp.dsp_version] value is the same as setting [15.A.00.069_20241211]
Jan 25 00:00:03 runsvdir: libc: Unable to set property "ro.bsp.dsp_version" to "15.A.00.069_20241211": error code: 0xb
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.864] [common/common.c] [update_property_file-1392] : set property [ro.bsp.dsp_version]=[15.A.00.069_20241211] failed
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.864] [upgrade/upgrade.c] [set_upgrade_progress-166] : [0 0]
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.864] [upgrade/upgrade.c] [set_upgrade_status-156] : set upgrade status : false
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.883] [hardware/DSP_IA8201/src/dsp_mode_set.c] [Route_GetMode-440] : try lock dsp
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.884] [hardware/DSP_IA8201/src/dsp_mode_set.c] [Route_GetMode-462] : get mode [1], return [0]
Jan 25 00:00:03 runsvdir: [command_service] [20270125 000003.884] [sub_thread/communicate_to_audio_service.c] [handle_audio_service_msg-104] : get dsp mode back over : 1
Jan 25 00:00:03 runsvdir: [audio service]:read_dsp_characteristic reg[1][0] length[1]
Jan 25 00:00:03 runsvdir: [audio service]:set_dsp_mode result[0]
Jan 25 00:00:03 runsvdir: [audio service]:dsp mode set to 1
Jan 25 00:00:03 runsvdir: [audio service]:snd_ctl_event wait event
Jan 25 00:00:04 runsvdir: [audio service]:snd_numid_handle :7 48000
Jan 25 00:00:04 runsvdir: [audio service]:snd_numid_handle :7 0
Jan 25 00:00:04 runsvdir: [audio service]:snd_numid_handle :4 48000
Jan 25 00:00:04 runsvdir: [audio service]:dsp_mode_without_mic :1
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.366] [hardware/DSP_IA8201/src/dsp_mode_set.c] [Route_SetMode-472] : try lock dsp
Jan 25 00:00:04 runsvdir: [audio service]:UVC_AUDIO_Ai exit
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.378] [hardware/DSP_IA8201/src/dsp_mode_set.c] [Route_SetMode-509] : set mode [3], return [0]
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.379] [sub_thread/communicate_to_audio_service.c] [handle_audio_service_msg-126] : set dsp mode back over : 0
Jan 25 00:00:04 runsvdir: [audio service]:buffer_size 1024
Jan 25 00:00:04 runsvdir: [audio service]:period_size 256
Jan 25 00:00:04 runsvdir: [audio service]:app_ring_buffer_readn buff_size[2048] s32Ret[0]
Jan 25 00:00:04 runsvdir: [audio service]:UVC_AUDIO_Ai run
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.429] [hardware/DSP_IA8201/src/dsp_mode_set.c] [Route_GetMode-440] : try lock dsp
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.430] [hardware/DSP_IA8201/src/dsp_mode_set.c] [Route_GetMode-462] : get mode [1], return [0]
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.430] [sub_thread/communicate_to_audio_service.c] [handle_audio_service_msg-104] : get dsp mode back over : 1
Jan 25 00:00:04 runsvdir: [audio service]:read_dsp_characteristic reg[1][0] length[1]
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.480] [hardware/DSP_IA8201/src/dsp_mode_set.c] [Route_GetMode-440] : try lock dsp
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.481] [hardware/DSP_IA8201/src/dsp_mode_set.c] [Route_GetMode-462] : get mode [3], return [0]
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.481] [sub_thread/communicate_to_audio_service.c] [handle_audio_service_msg-104] : get dsp mode back over : 3
Jan 25 00:00:04 runsvdir: [audio service]:read_dsp_characteristic reg[3][0] length[1]
Jan 25 00:00:04 runsvdir: [audio service]:set_dsp_mode result[0]
Jan 25 00:00:04 runsvdir: [audio service]:dsp mode set 3
Jan 25 00:00:04 runsvdir: [audio service]:snd_numid_handle :4 0
Jan 25 00:00:04 runsvdir: [audio service]:dsp_mode_without_mic :1
Jan 25 00:00:04 runsvdir: [audio service]:UVC_AUDIO_Ai Thread is paused...
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.531] [hardware/DSP_IA8201/src/dsp_mode_set.c] [Route_SetMode-472] : try lock dsp
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.543] [hardware/DSP_IA8201/src/dsp_mode_set.c] [Route_SetMode-509] : set mode [1], return [0]
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.543] [sub_thread/communicate_to_audio_service.c] [handle_audio_service_msg-126] : set dsp mode back over : 0
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.594] [hardware/DSP_IA8201/src/dsp_mode_set.c] [Route_GetMode-440] : try lock dsp
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.594] [hardware/DSP_IA8201/src/dsp_mode_set.c] [Route_GetMode-462] : get mode [3], return [0]
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.594] [sub_thread/communicate_to_audio_service.c] [handle_audio_service_msg-104] : get dsp mode back over : 3
Jan 25 00:00:04 runsvdir: [audio service]:read_dsp_characteristic reg[3][0] length[1]
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.644] [hardware/DSP_IA8201/src/dsp_mode_set.c] [Route_GetMode-440] : try lock dsp
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.645] [hardware/DSP_IA8201/src/dsp_mode_set.c] [Route_GetMode-462] : get mode [1], return [0]
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.645] [sub_thread/communicate_to_audio_service.c] [handle_audio_service_msg-104] : get dsp mode back over : 1
Jan 25 00:00:04 runsvdir: [audio service]:read_dsp_characteristic reg[1][0] length[1]
Jan 25 00:00:04 runsvdir: [audio service]:set_dsp_mode result[0]
Jan 25 00:00:04 runsvdir: [audio service]:dsp mode set to 1
Jan 25 00:00:04 runsvdir: [audio service]:snd_numid_handle :6 0
Jan 25 00:00:04 runsvdir: [audio service]:hsv[0],index[0]dspv[1000]
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.695] [hardware/DSP_IA8201/src/dsp_mode_set.c] [route_set_volume-539] : setting volume 1000, register value 000be800
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.697] [hardware/DSP_IA8201/src/dsp_mode_set.c] [route_set_volume-552] : set volume [1000], return [0]
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.747] [hardware/DSP_IA8201/src/dsp_mode_set.c] [route_read_volume-560] : try lock dsp
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.748] [hardware/DSP_IA8201/src/dsp_mode_set.c] [route_read_volume-582] : read volume [1000], return [0]
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.748] [sub_thread/communicate_to_audio_service.c] [handle_audio_service_msg-191] : get dsp volume back over : 1000
Jan 25 00:00:04 runsvdir: [audio service]:read_dsp_characteristic reg[e8][3] length[2]
Jan 25 00:00:04 runsvdir: [audio service]:set_dsp_volume result[0] volume[1000]
Jan 25 00:00:04 runsvdir: [audio service]:volume changing , volume is:1000 0
Jan 25 00:00:04 runsvdir: [audio service]:snd_numid_handle :3 0
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.765] [function/upgrade_function.c] [read_system_version-579] : os version : 15.1.02.658_USERROOT
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.766] [common/common.c] [get_func_by_msgid-993] : unknown msgid : 00d4
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.778] [function/upgrade_function.c] [read_system_version-579] : os version : 15.1.02.658_USERROOT
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.779] [common/common.c] [get_func_by_msgid-993] : unknown msgid : 00d4
Jan 25 00:00:04 runsvdir: [usb_gadget_service] DEBUG Service.cpp onTransact:129: Received string: get config
Jan 25 00:00:04 runsvdir: [usb_gadget_service] ERROR Service.cpp parseUsbGadgetConfig:79: Invalid config segment: get config
Jan 25 00:00:04 runsvdir: [usb_gadget_service] DEBUG Service.cpp onTransact:139: usb gadget no change
Jan 25 00:00:04 runsvdir: [usb_gadget_client] DEBUG nr_usb_gadget.cpp get_usb_gadget_config:234: Configuration get: ncm=enable,ecm=enable,mtp=enable,hid=enable,uac=enable
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.793] [function/upgrade_function.c] [read_system_version-579] : os version : 15.1.02.658_USERROOT
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.793] [common/common.c] [get_func_by_msgid-993] : unknown msgid : 00d4
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.803] [function/upgrade_function.c] [read_system_version-579] : os version : 15.1.02.658_USERROOT
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.804] [common/common.c] [get_func_by_msgid-993] : unknown msgid : 00d4
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.804] [common/common.c] [wait_property_ready-1174] : waiting for property [sys.bsp.cam_plug_status] value [plug_in], force waiting [false], timeout 2 s
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.804] [common/common.c] [wait_property_ready-1183] : get value
Jan 25 00:00:04 runsvdir: [command_service] [20270125 000004.804] [common/common.c] [wait_property_ready-1218] : property [sys.bsp.cam_plug_status], aiming value [plug_in], read value [plug_in]
Jan 25 00:00:04 runsvdir: [storage_service] DEBUG storage.c get_usb_storage_mode_config:780: 
Jan 25 00:00:04 runsvdir: [usb_gadget_service] DEBUG Service.cpp onTransact:129: Received string: get config
Jan 25 00:00:04 runsvdir: [usb_gadget_service] ERROR Service.cpp parseUsbGadgetConfig:79: Invalid config segment: get config
Jan 25 00:00:04 runsvdir: [usb_gadget_service] DEBUG Service.cpp onTransact:139: usb gadget no change
Jan 25 00:00:04 runsvdir: [usb_gadget_client] DEBUG nr_usb_gadget.cpp get_usb_gadget_config:234: Configuration get: ncm=enable,ecm=enable,mtp=enable,hid=enable,uac=enable
Jan 25 00:00:05 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:05 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:05 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:05 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:05 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:05 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:05 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:05 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:05 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:05 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:05 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:05 rcS: not found hdcp node
Jan 25 00:00:05 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:05 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:05 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:05 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:05 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:05 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:05 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:05 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:05 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:05 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:05 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:05 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:05 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:05 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:05 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:05 runsvdir: set S_VDD_0V8 and S_VDD_DOV8 to 775mV
Jan 25 00:00:05 runsvdir: DisplayService I : <timestamp open/close oled> -- [ 4.20 ]
Jan 25 00:00:05 runsvdir: DisplayService I : Screen successfully opened.
Jan 25 00:00:05 runsvdir: DisplayService I : set duty 30
Jan 25 00:00:05 runsvdir: DisplayService I : check oled status:open, ret = 0
Jan 25 00:00:05 runsvdir: DisplayService I : set duty 30
Jan 25 00:00:05 runsvdir: DisplayService W : can't find node 0x7f8c0020a0, ignore this release
Jan 25 00:00:05 runsvdir: DisplayService W : can't find node 0x7f8c002370, ignore this release
Jan 25 00:00:05 runsvdir: DisplayService I : get edid 9, dp_audio 0
Jan 25 00:00:05 runsvdir: DisplayService I : current vo's refresh rate 90Hz, return 3
Jan 25 00:00:05 runsvdir: DisplayService I : get edid 9, dp_audio 0
Jan 25 00:00:05 runsvdir: DisplayService I : get dp info 1920x1080@90
Jan 25 00:00:05 runsvdir: DisplayService I : Get frame stats: Successes 62.00, Failures 0.33
Jan 25 00:00:05 runsvdir: DisplayService I : dpvsync : 0.00
Jan 25 00:00:05 runsvdir: DisplayService I : DPISP LUT mode matches, expected mode: 0, current mode: 0 (pipe: 2)
Jan 25 00:00:05 runsvdir: DisplayService I : set dpisp filter mode:1
Jan 25 00:00:05 runsvdir: DisplayService I : Setting new LUT for mode: dp_scaler_lut_direct_mode_1
Jan 25 00:00:05 runsvdir: DisplayService I : get edid 9, dp_audio 0
Jan 25 00:00:05 runsvdir: DisplayService I : get edid 9, dp_audio 0
Jan 25 00:00:05 runsvdir: DisplayService I : check oled status:open, ret = 0
Jan 25 00:00:05 runsvdir: DisplayService I : set duty 0
Jan 25 00:00:05 runsvdir: DisplayService I : <timestamp open/close oled> -- [ 5.570 ]
Jan 25 00:00:05 runsvdir: DisplayService I : Screen successfully closed.
Jan 25 00:00:05 runsvdir: DisplayService I : switch edid to 5 without audio
Jan 25 00:00:05 runsvdir: DisplayService I : dpisp get a new state:0
Jan 25 00:00:05 runsvdir: DisplayService I : <timestamp get a video change interrupt> -- [ 5.581 ]
Jan 25 00:00:05 runsvdir: DisplayService I : <timestamp DPSIP stop start> -- [ 5.581 ]
Jan 25 00:00:05 runsvdir: DisplayService I : <timestamp force release vb done> -- [ 5.581 ]
Jan 25 00:00:05 runsvdir: DisplayService I : free vb count = 8, inused vb count = 0
Jan 25 00:00:05 runsvdir: DisplayService I : <timestamp DPSIP stop done> -- [ 5.600 ]
Jan 25 00:00:05 runsvdir: DisplayService D : destory pipi = 2, isp object = 0x7f91f88108
Jan 25 00:00:05 runsvdir: DisplayService W : can't find node 0x7f8c002be0, ignore this release
Jan 25 00:00:05 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:05 runsvdir: DisplayService I : get edid 0, dp_audio 0
Jan 25 00:00:05 runsvdir: DisplayService I : <timestamp destory vi done> -- [ 5.601 ]
Jan 25 00:00:05 runsvdir: DisplayService I : current dpisp status update :0
Jan 25 00:00:05 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:05 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:05 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:05 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:05 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:05 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:05 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:05 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:05 runsvdir: DisplayService I : <timestamp DP status changed start> -- [ 5.781 ]
Jan 25 00:00:05 runsvdir: DisplayService I : dp status changed, 1920x1080@90 -> 0x0@0
Jan 25 00:00:05 runsvdir: DisplayService I : err: fps:0, H:0, V:0
Jan 25 00:00:05 runsvdir: DisplayService I : <timestamp DP status changed end> -- [ 5.781 ]
Jan 25 00:00:05 runsvdir: DisplayService I : notify dp info update
Jan 25 00:00:05 runsvdir: DisplayService I : get a dp node, state 2
Jan 25 00:00:05 runsvdir: DisplayService I : broadcast dp event : 2
Jan 25 00:00:05 runsvdir: DisplayService I : release this event
Jan 25 00:00:05 runsvdir: DisplayService I : check oled status:close, ret = 0
Jan 25 00:00:05 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:05 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:05 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:05 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:05 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:05 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:05 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:05 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:05 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:05 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:05 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:05 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:05 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:05 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:05 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:05 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:05 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:05 runsvdir: DisplayService W : dpisp current s^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:05 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:05 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:05 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:05 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:05 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:05 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:05 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:05 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:05 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:05 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:05 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:05 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:05 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:05 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: tate is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService I : <timestamp send edid end> -- [ 7.71 ]
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:06 runsvdir: Di^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:06 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:07 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:07 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:07 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:07 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:07 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:07 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:07 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:07 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:07 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:07 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:07 runsvdir: ^[[0;31mDisplayService E: __handleClientRequest[1559] get frame failed^[[0;39m
Jan 25 00:00:07 runsvdir: 3 cam_id[0], first frame done 1^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124390657][0300][0x7f81616200][SYS_CORE][VI_KEY] vif_mid.c: vif_bpintr_process : 1173 cam_id[0], first frame done 1^[[0m
Jan 25 00:00:07 runsvdir: ^[[33;22m[124390659][0300][0x7f809a5200][VO_CORE][WARN] display_top.c: display_offset_vo_src : 1520 hardware_diff_us:6635.126465 first_skewing_us:6635.126465!
Jan 25 00:00:07 runsvdir: ^[[0m
Jan 25 00:00:07 runsvdir: w h change from 1920 1080  to 64 36 fetch scaler lut 
Jan 25 00:00:07 runsvdir: ^[[31;22m[124390660][0300][0x7f81616200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:07 runsvdir: out and in is not equal, use uvsmooth lut table 
Jan 25 00:00:07 runsvdir: ^[[35;22m[124390661][0300][0x7f81616200][SYS_CORE][VI_KEY] vif_mid.c: vif_bpintr_process : 1173 cam_id[0], first frame done 2^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124390661][0300][0x7f81616200][SYS_CORE][VI_KEY] vif_mid.c: vif_bpintr_process : 1173 cam_id[0], first frame done 2^[[0m
Jan 25 00:00:07 runsvdir: w h change from 64 36  to 1920 1080 fetch scaler lut 
Jan 25 00:00:07 runsvdir: ^[[31;22m[124390680][0300][0x7f81616200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:07 runsvdir: out and in is equal, use y bypass lut table 
Jan 25 00:00:07 runsvdir: ^[[33;22m[124390734][0300][0x7f81267200][AIO_CORE][WARN] i2s.c: ar_i2s_io_dev_dma_stop : 1378 s32ChnEnCnt = 0 u32ChnCnt 2.
Jan 25 00:00:07 runsvdir: ^[[0m
Jan 25 00:00:07 runsvdir: [ahb-4] start llp_pa:0x3cc1d000 llp:0x7f80a1b000!
Jan 25 00:00:07 runsvdir: [ahb-4] src:0x13111c0 dest:0x3cc0d000 ctl_l:0x1a209425!
Jan 25 00:00:07 runsvdir: ^[[33;22m[124390735][0300][0x7f8081b200][AIO_CORE][WARN] i2s.c: ar_i2s_io_dev_dma_start : 1345 Ai s32ChnEnCnt = 1 u32ChnCnt 2.
Jan 25 00:00:07 runsvdir: ^[[0m
Jan 25 00:00:07 runsvdir: w h change from 1920 1080  to 1920 1080 fetch scaler lut 
Jan 25 00:00:07 runsvdir: ^[[31;22m[124390806][0300][0x7f81616200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [dp_scaler_lut_direct_mode_1] failed!^[[0m
Jan 25 00:00:07 runsvdir: yuv420 out, user have set the lut, use user lut
Jan 25 00:00:07 runsvdir: ^[[35;22m[124390812][0300][0x7f8081b200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124390813][0300][0x7f8081b200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124390813][0300][0x7f80a45200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_close_camera : 961 camera_id=1^[[0m
Jan 25 00:00:07 runsvdir: ^[[31;22m[124391007][0300][0x7f809a5200][VO_CORE][ERROR] display_top.c: display_offset_vo_src : 1510 src_de_over_time_us:-14204.160156 is more than mod_de_time_us:11110.156250(one frame), skewing_err_cnt:1^[[0m
Jan 25 00:00:07 runsvdir: reference_clock:        300.000000Mhz
Jan 25 00:00:07 runsvdir: src_vsync_sel:          SRC_DP_ISP
Jan 25 00:00:07 runsvdir: de_vsync_sel:           DE1
Jan 25 00:00:07 runsvdir: src_vsync_polarity:     0
Jan 25 00:00:07 runsvdir: de_vsync_polarity:      0
Jan 25 00:00:07 runsvdir: src_frame               16666.083984 us, 60.002098 fps
Jan 25 00:00:07 runsvdir: de_frame                11110.156250 us, 90.007735 fps
Jan 25 00:00:07 runsvdir: vsync_delay_time(limit) 8016.156738 us
Jan 25 00:00:07 runsvdir: vsync_delay_time(over)  -14204.160156 us
Jan 25 00:00:07 runsvdir: open the driver sns:dp_rx obj:stSnsdp_rxObj lib:libsns_dp_rx.so 
Jan 25 00:00:07 runsvdir: ^[[31;22m[25 00:00:07.212481][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3559 !!!!!!the vif is force to dpcm duce to reduce bandwidth, if you want to use the dng, please set the pstPipeAttr->enEnableN
Jan 25 00:00:07 runsvdir: ^[[31;22m[25 00:00:07.212515][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3603 !!!!!!! Note: your app cfg the vif write compress on,so you must cfg the vif fre >=3*mipi fre !!!!!!^[[0m
Jan 25 00:00:07 runsvdir: ^[[31;22m[25 00:00:07.212902][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7846 not supported addr type^[[0m
Jan 25 00:00:07 runsvdir: ^[[31;22m[25 00:00:07.212929][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7856 not supported data type^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391018][0300][0x7f80759200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_open_camera : 920 camera_id=1^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391019][0300][0x7f8083c200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391019][0300][0x7f8083c200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:07 runsvdir: ^[[33;22m[124391019][0300][0x7f8083c200][VO_CORE][WARN] display_api.c: dc_deinit : 2323 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391019][0300][0x7f813ae200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391019][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;2splayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:07 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:07 runsvdir: DisplayService I : Get frame stats: Successes 36.33, Failures 29.00
Jan 25 00:00:07 runsvdir: DisplayService I : dpvsync : 0.00
Jan 25 00:00:07 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:07 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:07 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:07 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:07 runsvdir: DisplayService I : <timestamp get a video change interrupt> -- [ 7.451 ]
Jan 25 00:00:07 runsvdir: DisplayService I : pixel clock: 297000000, fps = 60
Jan 25 00:00:07 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:07 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:07 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:07 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:07 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:07 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:07 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:07 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:07 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:07 runsvdir: DisplayService W : dpisp current state is 0, can't get frame form it
Jan 25 00:00:07 runsvdir: DisplayService I : <timestamp DP status changed start> -- [ 7.651 ]
Jan 25 00:00:07 runsvdir: DisplayService I : dp status changed, 0x0@0 -> 3840x1080@60
Jan 25 00:00:07 runsvdir: DisplayService I : <timestamp DP status changed end> -- [ 7.651 ]
Jan 25 00:00:07 runsvdir: DisplayService I : notify dp info update
Jan 25 00:00:07 runsvdir: DisplayService I : get a dp node, state 1
Jan 25 00:00:07 runsvdir: DisplayService I : should init mppvi
Jan 25 00:00:07 runsvdir: DisplayService I : broadcast dp event : 1
Jan 25 00:00:07 runsvdir: DisplayService I : dpisp get a new state:1
Jan 25 00:00:07 runsvdir: DisplayService D : get dprx so
Jan 25 00:00:07 runsvdir: DisplayService D : init pipi = 2, isp object = 0x7f91f88108
Jan 25 00:00:07 runsvdir: DisplayService I : enPixelFormat:0x17
Jan 25 00:00:07 runsvdir: DisplayService I : get dpvif_fre_hz = 333000000
Jan 25 00:00:07 runsvdir: DisplayService I : >>>>>> stComboAttr >>>>>>>
Jan 25 00:00:07 runsvdir: DisplayService I : devno: 0
Jan 25 00:00:07 runsvdir: DisplayService I : input_mode: 0x0b
Jan 25 00:00:07 runsvdir: DisplayService I : data_rate : 450
Jan 25 00:00:07 runsvdir: DisplayService I : mipi-input_data_type: 0x02
Jan 25 00:00:07 runsvdir: DisplayService I : stSnsSize.u32Width = 3840
Jan 25 00:00:07 runsvdir: DisplayService I : stSnsSize.u32Height = 1080
Jan 25 00:00:07 runsvdir: DisplayService I : stTiming.hblank = 560
Jan 25 00:00:07 runsvdir: DisplayService I : stTiming.vblank = 45
Jan 25 00:00:07 runsvdir: DisplayService I : stChnAttr.stSize.u32Width = 3840
Jan 25 00:00:07 runsvdir: DisplayService I : stChnAttr.stSize.u32Height = 1080
Jan 25 00:00:07 runsvdir: DisplayService I : stChnAttr.u32BufCount = 8
Jan 25 00:00:07 runsvdir: DisplayService I : stPubAttr.f32FrameRate = 60.000000
Jan 25 00:00:07 runsvdir: DisplayService I : <<<<<< stComboAttr <<<<<<<
Jan 25 00:00:07 runsvdir: DisplayService I : release this event
Jan 25 00:00:07 runsvdir: DisplayService I : get dp info 3840x1080@60
Jan 25 00:00:07 runsvdir: read vb config data index:4 success.
Jan 25 00:00:07 runsvdir: DisplayService I : pipe:2 channel:0 get vb pool id <4>!
Jan 25 00:00:07 runsvdir: read vb config data index:4 success.
Jan 25 00:00:07 runsvdir: get vb pool id <4>!
Jan 25 00:00:07 runsvdir: DisplayService I : pipe:2 chn:0 bind vb pool:4 success!
Jan 25 00:00:07 runsvdir: DisplayService I : get dp info 3840x1080@60
Jan 25 00:00:07 runsvdir: DisplayService I : get a cmd: stop dpisp
Jan 25 00:00:07 runsvdir: DisplayService I : isp attr: type : bt709, color range : full range
Jan 25 00:00:07 runsvdir: DisplayService I : <timestamp init vi done> -- [ 7.677 ]
Jan 25 00:00:07 runsvdir: DisplayService I : current dpisp status update :1
Jan 25 00:00:07 runsvdir: DisplayService I : dpisp get a new state:0
Jan 25 00:00:07 runsvdir: DisplayService I : <timestamp DPSIP stop start> -- [ 7.677 ]
Jan 25 00:00:07 runsvdir: DisplayService I : <timestamp force release vb done> -- [ 7.677 ]
Jan 25 00:00:07 runsvdir: DisplayService I : free vb count = 8, inused vb count = 0
Jan 25 00:00:07 runsvdir: DisplayService I : <timestamp DPSIP stop done> -- [ 7.679 ]
Jan 25 00:00:07 runsvdir: DisplayService D : destory pipi = 2, isp object = 0x7f91f88108
Jan 25 00:00:07 runsvdir: DisplayService I : <timestamp destory vi done> -- [ 7.680 ]
Jan 25 00:00:07 runsvdir: DisplayService I : current dpisp status update :0
Jan 25 00:00:07 runsvdir: DisplayOLED I : screen soft in change refresh reset successfully!
Jan 25 00:00:07 runsvdir: DisplayOLED I : OLED no set force lane!
Jan 25 00:00:07 runsvdir: DisplayOLED I : GF screen set inverse scan direction
Jan 25 00:00:07 runsvdir: DisplayService I : Configure OLED to 5lanesX2@90Hz Success
Jan 25 00:00:07 runsvdir: DisplayService I : get cmd CMD_VI_RESUME_BY_VO
Jan 25 00:00:07 runsvdir: DisplayService I : dpisp get a new state:1
Jan 25 00:00:07 runsvdir: DisplayService D : get dprx so
Jan 25 00:00:07 runsvdir: DisplayService D : init pipi = 2, isp object = 0x7f91f88108
Jan 25 00:00:07 runsvdir: DisplayService I : enPixelFormat:0x17
Jan 25 00:00:07 runsvdir: DisplayService I : get dpvif_fre_hz = 333000000
Jan 25 00:00:07 runsvdir: DisplayService I : >>>>>> stComboAttr >>>>>>>
Jan 25 00:00:07 runsvdir: DisplayService I : devno: 0
Jan 25 00:00:07 runsvdir: DisplaySer2m[124391019][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:00:07 runsvdir: 36e7d000 372b7000 373c7000 0 0 0 374d1000 3790b000 37a1b000 0 0 0 
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391019][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:07 runsvdir: ^[[33;22m[124391019][0300][0x7f80759200][SYS_CORE][WARN] ar_subvision_ctrl.c: ar_subvision_vsync_statistic_disable : 483 subvision already disable!^[[0m
Jan 25 00:00:07 runsvdir: ^[[33;22m[124391019][0300][0x7f80759200][VO_CORE][WARN] display_api.c: dc_deinit : 2323 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:00:07 runsvdir: aec index 198685398 line 1125 gain 1.000000 
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391019][0300][0x7f813ae200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391019][0300][0x7f813ae200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391019][0300][0x7f808c0200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391020][0300][0x7f808c0200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391021][0300][0x7f808c0200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_close_camera : 961 camera_id=1^[[0m
Jan 25 00:00:07 runsvdir: open the driver sns:dp_rx obj:stSnsdp_rxObj lib:libsns_dp_rx.so 
Jan 25 00:00:07 runsvdir: ^[[31;22m[25 00:00:07.265367][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3559 !!!!!!the vif is force to dpcm duce to reduce bandwidth, if you want to use the dng, please set the pstPipeAttr->enEnableN
Jan 25 00:00:07 runsvdir: ^[[31;22m[25 00:00:07.265419][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3603 !!!!!!! Note: your app cfg the vif write compress on,so you must cfg the vif fre >=3*mipi fre !!!!!!^[[0m
Jan 25 00:00:07 runsvdir: ^[[31;22m[25 00:00:07.265550][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7846 not supported addr type^[[0m
Jan 25 00:00:07 runsvdir: ^[[31;22m[25 00:00:07.265561][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7856 not supported data type^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391024][0300][0x7f812a9200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_open_camera : 920 camera_id=1^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391024][0300][0x7f80a45200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391024][0300][0x7f80a45200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391024][0300][0x7f82382200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391024][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:00:07 runsvdir: 36e7d000 372b7000 373c7000 0 0 0 374d1000 3790b000 37a1b000 0 0 0 
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391024][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391024][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:07 runsvdir: aec index 198685398 line 1125 gain 1.000000 
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391025][0300][0x7f82382200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391025][0300][0x7f82382200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:07 runsvdir: 374d7000 37911000 37a21000 0 0 0 37b2b000 37f65000 38075000 0 0 0 
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391027][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391028][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391041][0300][0x7f8081b200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:07 runsvdir: ^[[35;22m[124391042][0300][0x7f8081b200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:00:07 runsvdir: ^[[31;22m[124391044][0498][0x7f6ffff1a0][VPSS_MPP][ERROR] mpi_vpss.c: ar_hal_vpss_create : 870 bind_register_sender failed 0x80028003^[[0m
Jan 25 00:00:07 runsvdir: ^[[33;22m[124391044][0300][0x7f80738200][VO_CORE][WARN] display_api.c: dc_init : 2258 use dc_reg_irq_by_cmd!^[[0m
Jan 25 00:00:10 runsvdir: ^[[31;22m[124391044][0300][0x7f80738200][VO_CORE][ERROR] display_top.c: ar_di[audio service]:snd_numid_handle :7 48000
Jan 25 00:00:10 runsvdir: set S_VDD_0V8 and S_VDD_DOV8 to 762.5mV
Jan 25 00:00:11 runsvdir: vice I : input_mode: 0x0b
Jan 25 00:00:11 runsvdir: DisplayService I : data_rate : 450
Jan 25 00:00:11 runsvdir: DisplayService I : mipi-input_data_type: 0x02
Jan 25 00:00:11 runsvdir: DisplayService I : stSnsSize.u32Width = 3840
Jan 25 00:00:11 runsvdir: DisplayService I : stSnsSize.u32Height = 1080
Jan 25 00:00:11 runsvdir: DisplayService I : stTiming.hblank = 560
Jan 25 00:00:11 runsvdir: DisplayService I : stTiming.vblank = 45
Jan 25 00:00:11 runsvdir: DisplayService I : stChnAttr.stSize.u32Width = 3840
Jan 25 00:00:11 runsvdir: DisplayService I : stChnAttr.stSize.u32Height = 1080
Jan 25 00:00:11 runsvdir: DisplayService I : stChnAttr.u32BufCount = 8
Jan 25 00:00:11 runsvdir: DisplayService I : stPubAttr.f32FrameRate = 60.000000
Jan 25 00:00:11 runsvdir: DisplayService I : <<<<<< stComboAttr <<<<<<<
Jan 25 00:00:11 runsvdir: read vb config data index:4 success.
Jan 25 00:00:11 runsvdir: DisplayService I : pipe:2 channel:0 get vb pool id <4>!
Jan 25 00:00:11 runsvdir: read vb config data index:4 success.
Jan 25 00:00:11 runsvdir: get vb pool id <4>!
Jan 25 00:00:11 runsvdir: DisplayService I : pipe:2 chn:0 bind vb pool:4 success!
Jan 25 00:00:11 runsvdir: DisplayService I : <timestamp AR_MPI_VI_GetHightPricisionPipeFPS start> -- [ 7.714 ]
Jan 25 00:00:11 runsvdir: DisplayService I : get high pricision fps = 60.002090
Jan 25 00:00:11 runsvdir: DisplayService I : <timestamp AR_MPI_VI_GetHightPricisionPipeFPS end> -- [ 7.874 ]
Jan 25 00:00:11 runsvdir: DisplayService I : isp attr: type : bt709, color range : full range
Jan 25 00:00:11 runsvdir: DisplayService I : <timestamp init vi done> -- [ 7.889 ]
Jan 25 00:00:11 runsvdir: DisplayService I : current dpisp status update :1
Jan 25 00:00:11 runsvdir: DisplayService I : get a cmd: start dpisp
Jan 25 00:00:11 runsvdir: DisplayService I : dpisp get a new state:2
Jan 25 00:00:11 runsvdir: DisplayService I : only enable dpisp
Jan 25 00:00:11 runsvdir: DisplayService I : <timestamp init isp > -- [ 7.912 ]
Jan 25 00:00:11 runsvdir: DisplayService I : enable vi channel output
Jan 25 00:00:11 runsvdir: DisplayService I : check oled status:close, ret = 0
Jan 25 00:00:11 runsvdir: DisplayService I : set duty 0
Jan 25 00:00:11 runsvdir: DisplayService I : Setting new LUT for mode: dp_scaler_lut_direct_mode_1
Jan 25 00:00:11 runsvdir: DisplayService I : <timestamp init isp done> -- [ 7.919 ]
Jan 25 00:00:11 runsvdir: DisplayService I : current dpisp status update :2
Jan 25 00:00:11 runsvdir: DisplayService W : can't find node 0x7f8c002eb0, ignore this release
Jan 25 00:00:11 runsvdir: DisplayService I : <timestamp open/close oled> -- [ 7.935 ]
Jan 25 00:00:11 runsvdir: DisplayService I : Screen successfully opened.
Jan 25 00:00:11 runsvdir: DisplayService I : set duty 30
Jan 25 00:00:11 runsvdir: DisplayService I : check oled status:open, ret = 0
Jan 25 00:00:11 runsvdir: DisplayService I : set duty 30
Jan 25 00:00:11 runsvdir: DisplayService W : can't find node 0x7f8c003180, ignore this release
Jan 25 00:00:11 runsvdir: DisplayService I : get dp info 3840x1080@60
Jan 25 00:00:11 runsvdir: DisplayService I : get dp info 3840x1080@60
Jan 25 00:00:11 runsvdir: DisplayService I : get dp info 3840x1080@60
Jan 25 00:00:11 runsvdir: DisplayService I : get dp info 3840x1080@60
Jan 25 00:00:11 runsvdir: DisplayService I : enable dpisp vsync
Jan 25 00:00:11 runsvdir: DisplayService I : Get frame stats: Successes 49.00, Failures 4.67
Jan 25 00:00:11 runsvdir: DisplayService I : dpvsync : 4.67
Jan 25 00:00:11 runsvdir: DisplayService I : get dp info 3840x1080@60
Jan 25 00:00:11 runsvdir: DisplayService I : check oled status:open, ret = 0
Jan 25 00:00:11 runsvdir: DisplayService I : set duty 0
Jan 25 00:00:11 runsvdir: DisplayService I : <timestamp open/close oled> -- [ 11.826 ]
Jan 25 00:00:11 runsvdir: DisplayService I : Screen successfully closed.
Jan 25 00:00:11 runsvdir: DisplayService I : get dp info 3840x1080@60
Jan 25 00:00:11 runsvdir: DisplayService I : get a cmd: stop dpisp
Jan 25 00:00:11 runsvdir: DisplayService I : dpisp get a new state:0
Jan 25 00:00:11 runsvdir: DisplayService I : <timestamp DPSIP stop start> -- [ 11.880 ]
Jan 25 00:00:11 runsvdir: DisplayService I : <timestamp force release vb done> -- [ 11.880 ]
Jan 25 00:00:11 runsvdir: DisplayService I : free vb count = 8, inused vb count = 0
Jan 25 00:00:11 runsvdir: DisplayService I : <timestamp DPSIP stop done> -- [ 11.899 ]
Jan 25 00:00:11 runsvdir: DisplayService D : destory pipi = 2, isp object = 0x7f91f88108
Jan 25 00:00:11 runsvdir: DisplayService I : <timestamp destory vi done> -- [ 11.900 ]
Jan 25 00:00:11 runsvdir: DisplayService I : current dpisp status update :0
Jan 25 00:00:11 runsvdir: DisplayOLED I : screen soft in change refresh reset successfully!
Jan 25 00:00:11 runsvdir: DisplayOLED I : OLED no set force lane!
Jan 25 00:00:11 runsvdir: DisplayOLED I : GF screen set inverse scan direction
Jan 25 00:00:11 runsvdir: DisplayService I : Configure OLED to 5lanesX2@90Hz Success
Jan 25 00:00:11 runsvdir: DisplayService I : get cmd CMD_VI_RESUME_BY_VO
Jan 25 00:00:11 runsvdir: DisplayService I : dpisp get a new state:1
Jan 25 00:00:11 runsvdir: DisplayService D : get dprx so
Jan 25 00:00:11 runsvdir: DisplayService D : init pipi = 2, isp object = 0x7f91f88108
Jan 25 00:00:11 runsvdir: DisplayService I : enPixelFormat:0x17
Jan 25 00:00:11 runsvdir: DisplayService I : get dpvif_fre_hz = 333000000
Jan 25 00:00:11 runsvdir: DisplayService I : >>>>>> stComboAttr >>>>>>>
Jan 25 00:00:11 runsvdir: DisplayService I : devno: 0
Jan 25 00:00:11 runsvdir: DisplayService I : input_mode: 0x0b
Jan 25 00:00:11 runsvdir: DisplayService I : data_rate : 450
Jan 25 00:00:11 runsvdir: DisplayService I : mipi-input_data_type: 0x02
Jan 25 00:00:11 runsvdir: DisplayService I : stSnsSize.u32Width = 3840
Jan 25 00:00:11 runsvdir: DisplayService I : stSnsSize.u32Height = 1080
Jan 25 00:00:11 runsvdir: DisplayService I : stTiming.hblank = 560
Jan 25 00:00:11 runsvdir: DisplayService I : stTiming.vblank = 45
Jan 25 00:00:11 runsvdir: DisplayService I : stChnAttr.stSize.u32Width = 3840
Jan 25 00:00:11 runsvdir: DisplayService I : stChnAttr.stSize.u32Height = 1080
Jan 25 00:00:11 runsvdir: DisplayService I : stChnAttr.u32BufCount = 8
Jan 25 00:00:11 runsvdir: DisplayService I : stPubAsplay_dev_enable : 3521 not support set irq_type:^[[33;22m[124391044][0300][0x7f80738200][AXIMGR_CORE][WARN] ar_aximgr_lb.c: ar_aximgr_lb_enable : 285 [LB] 1920 1024 1024^[[0m
Jan 25 00:00:11 runsvdir: ^[[33;22m[124391044][0300][0x7f81f5f200][AXIMGR_CORE][WARN] ar_aximgr_lb.c: ar_aximgr_lb_enable : 285 [LB] 1920 1024 1024^[[0m
Jan 25 00:00:11 runsvdir: ^[[35;22m[124391044][0300][0x7f8081b200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:00:11 runsvdir: ^[[35;22m[124391045][0300][0x7f8081b200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:11 runsvdir: ^[[35;22m[124391045][0300][0x7f82382200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:11 runsvdir: ^[[35;22m[124391045][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:00:11 runsvdir: ^[[35;22m[124391045][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:00:11 runsvdir: 36e7d000 372b7000 373c7000 0 0 0 374d1000 3790b000 37a1b000 0 0 0 
Jan 25 00:00:11 runsvdir: ^[[35;22m[124391045][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:11 runsvdir: 374d7000 37911000 37a21000 0 0 0 37b2b000 37f65000 38075000 0 0 0 
Jan 25 00:00:11 runsvdir: aec index 198685398 line 1125 gain 1.000000 
Jan 25 00:00:11 runsvdir: ^[[35;22m[124391045][0300][0x7f82382200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:00:11 runsvdir: ^[[35;22m[124391045][0300][0x7f82382200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:11 runsvdir: ^[[35;22m[124391047][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:00:11 runsvdir: ^[[35;22m[124391048][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:00:11 runsvdir: ^[[33;22m[124391441][0300][0x7f80a45200][VO_CORE][WARN] display_api.c: dc_deinit : 2323 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:00:11 runsvdir: ^[[33;22m[124391441][0300][0x7f813ae200][VO_CORE][WARN] display_api.c: dc_deinit : 2323 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:00:11 runsvdir: ^[[35;22m[124391441][0300][0x7f81f5f200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:11 runsvdir: ^[[35;22m[124391442][0300][0x7f81f5f200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:00:11 runsvdir: ^[[35;22m[124391443][0300][0x7f82382200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_close_camera : 961 camera_id=1^[[0m
Jan 25 00:00:11 runsvdir: open the driver sns:dp_rx obj:stSnsdp_rxObj lib:libsns_dp_rx.so 
Jan 25 00:00:11 runsvdir: ^[[31;22m[25 00:00:11.469399][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3559 !!!!!!the vif is force to dpcm duce to reduce bandwidth, if you want to use the dng, please set the pstPipeAttr->enEnableN
Jan 25 00:00:11 runsvdir: ^[[31;22m[25 00:00:11.469444][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3603 !!!!!!! Note: your app cfg the vif write compress on,so you must cfg the vif fre >=3*mipi fre !!!!!!^[[0m
Jan 25 00:00:11 runsvdir: ^[[31;22m[25 00:00:11.469585][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7846 not supported addr type^[[0m
Jan 25 00:00:11 runsvdir: ^[[31;22m[25 00:00:11.469597][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7856 not supported data type^[[0m
Jan 25 00:00:11 runsvdir: ^[[35;22m[124391444][0300][0x7f80759200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_open_camera : 920 camera_id=1^[[0m
Jan 25 00:00:11 runsvdir: ^[[35;22m[124391445][0300][0x7f8081b200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:00:11 runsvdir: ^[[35;22m[124391445][0300][0x7f8081b200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:11 runsvdir: ^[[35;22m[124391445][0300][0x7f8083c200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:11 runsvdir: ^[[35;22m[124391445][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:00:11 runsvdir: ^[[35;22m[124391445][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:00:11 runsvdir: 36e7d000 372b7000 373c7000 0 0 0 374d1000 3790b000 37a1b000 0 0 0 
Jan 25 00:00:11 runsvdir: ^[[35;22m[124391446][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:11 runsvdir: aec index 198685398 line 1125 gain 1.000000 
Jan 25 00:00:12 runsvdir: ^[[35;22m[124391446][0300][0x7f8083c200][SYS_CORE][VI_KALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:00:12 runsvdir: [audio service]:try_again
Jan 25 00:00:25 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:00:25 runsvdir: [audio service]:try_again
Jan 25 00:00:25 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:00:25 runsvdir: [audio service]:try_again
Jan 25 00:00:29 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:00:29 runsvdir: [audio service]:try_again
Jan 25 00:00:31 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:00:31 runsvdir: [audio service]:try_again
Jan 25 00:00:34 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:00:34 runsvdir: [audio service]:try_again
Jan 25 00:00:34 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:00:34 runsvdir: [audio service]:try_again
Jan 25 00:00:35 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:00:35 runsvdir: [audio service]:try_again
Jan 25 00:00:38 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:00:38 runsvdir: [audio service]:try_again
Jan 25 00:00:38 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:00:38 runsvdir: [audio service]:try_again
Jan 25 00:00:38 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:00:38 runsvdir: [audio service]:try_again
Jan 25 00:00:38 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:00:38 runsvdir: [audio service]:try_again
Jan 25 00:00:42 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:00:42 runsvdir: [audio service]:try_again
Jan 25 00:00:42 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:00:42 runsvdir: [audio service]:try_again
Jan 25 00:00:49 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:00:49 runsvdir: [audio service]:try_again
Jan 25 00:00:49 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:00:49 runsvdir: [audio service]:try_again
Jan 25 00:00:56 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:00:56 runsvdir: [audio service]:try_again
Jan 25 00:00:56 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:00:56 runsvdir: [audio service]:try_again
Jan 25 00:00:56 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:00:56 runsvdir: [audio service]:try_again
Jan 25 00:01:13 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:13 runsvdir: [audio service]:try_again
Jan 25 00:01:13 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:13 runsvdir: [audio service]:try_again
Jan 25 00:01:14 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:14 runsvdir: [audio service]:try_again
Jan 25 00:01:14 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:14 runsvdir: [audio service]:try_again
Jan 25 00:01:15 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:15 runsvdir: [audio service]:try_again
Jan 25 00:01:15 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:15 runsvdir: [audio service]:try_again
Jan 25 00:01:16 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:16 runsvdir: [audio service]:try_again
Jan 25 00:01:16 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:16 runsvdir: [audio service]:try_again
Jan 25 00:01:16 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:16 runsvdir: [audio service]:try_again
Jan 25 00:01:17 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:17 runsvdir: [audio service]:try_again
Jan 25 00:01:17 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:17 runsvdir: [audio service]:try_again
Jan 25 00:01:17 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:17 runsvdir: [audio service]:try_again
Jan 25 00:01:18 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:18 runsvdir: [audio service]:try_again
Jan 25 00:01:18 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:18 runsvdir: [audio service]:try_again
Jan 25 00:01:19 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:19 runsvdir: [audio service]:try_again
Jan 25 00:01:19 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:19 runsvdir: [audio service]:try_again
Jan 25 00:01:19 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:19 runsvdir: [audio service]:try_again
Jan 25 00:01:19 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:19 runsvdir: [audio service]:try_again
Jan 25 00:01:19 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:19 runsvdir: [audio service]:try_again
Jan 25 00:01:19 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:20 runsvdir: [audio service]:try_again
Jan 25 00:01:21 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:21 runsvdir: [audio service]:try_again
Jan 25 00:01:23 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:23 runsvdir: [audio service]:try_again
Jan 25 00:01:24 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:24 runsvdir: [audio service]:try_again
Jan 25 00:01:24 runsvdir: ttr.f32FrameRate = 60.000000
Jan 25 00:01:24 runsvdir: DisplayService I : <<<<<< stComboAttr <<<<<<<
Jan 25 00:01:24 runsvdir: read vb config data index:4 success.
Jan 25 00:01:24 runsvdir: DisplayService I : pipe:2 channel:0 get vb pool id <4>!
Jan 25 00:01:24 runsvdir: read vb config data index:4 success.
Jan 25 00:01:24 runsvdir: get vb pool id <4>!
Jan 25 00:01:24 runsvdir: DisplayService I : pipe:2 chn:0 bind vb pool:4 success!
Jan 25 00:01:24 runsvdir: DisplayService I : <timestamp AR_MPI_VI_GetHightPricisionPipeFPS start> -- [ 11.925 ]
Jan 25 00:01:24 runsvdir: DisplayService I : get high pricision fps = 60.002125
Jan 25 00:01:24 runsvdir: DisplayService I : <timestamp AR_MPI_VI_GetHightPricisionPipeFPS end> -- [ 12.85 ]
Jan 25 00:01:24 runsvdir: DisplayService I : isp attr: type : bt709, color range : full range
Jan 25 00:01:24 runsvdir: DisplayService I : <timestamp init vi done> -- [ 12.101 ]
Jan 25 00:01:24 runsvdir: DisplayService I : current dpisp status update :1
Jan 25 00:01:24 runsvdir: DisplayService I : get a cmd: start dpisp
Jan 25 00:01:24 runsvdir: DisplayService I : dpisp get a new state:2
Jan 25 00:01:24 runsvdir: DisplayService I : only enable dpisp
Jan 25 00:01:24 runsvdir: DisplayService I : <timestamp init isp > -- [ 12.117 ]
Jan 25 00:01:24 runsvdir: DisplayService I : enable vi channel output
Jan 25 00:01:24 runsvdir: DisplayService I : check oled status:close, ret = 0
Jan 25 00:01:24 runsvdir: DisplayService I : set duty 0
Jan 25 00:01:24 runsvdir: DisplayService I : Setting new LUT for mode: dp_scaler_lut_direct_mode_1
Jan 25 00:01:24 runsvdir: DisplayService I : <timestamp init isp done> -- [ 12.127 ]
Jan 25 00:01:24 runsvdir: DisplayService I : current dpisp status update :2
Jan 25 00:01:24 runsvdir: DisplayService I : <timestamp open/close oled> -- [ 12.139 ]
Jan 25 00:01:24 runsvdir: DisplayService I : Screen successfully opened.
Jan 25 00:01:24 runsvdir: DisplayService I : set duty 30
Jan 25 00:01:24 runsvdir: DisplayService I : check oled status:open, ret = 0
Jan 25 00:01:24 runsvdir: DisplayService I : set duty 30
Jan 25 00:01:24 runsvdir: DisplayService I : get dp info 3840x1080@60
Jan 25 00:01:24 runsvdir: DisplayService W : can't find node 0x7f8c002640, ignore this release
Jan 25 00:01:24 runsvdir: DisplayService W : can't find node 0x7f8c002910, ignore this release
Jan 25 00:01:24 runsvdir: DisplayService W : can't find node 0x7f8c002be0, ignore this release
Jan 25 00:01:24 runsvdir: DisplayService I : Get frame stats: Successes 53.33, Failures 0.00
Jan 25 00:01:24 runsvdir: DisplayService I : dpvsync : 58.33
Jan 25 00:01:24 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:24 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:24 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:24 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:24 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:24 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:24 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:24 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:24 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:24 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:24 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:24 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:24 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:24 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:24 runsvdir: DisplayService I : Get frame stats: Successes 60.33, Failures 0.00
Jan 25 00:01:24 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:24 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:24 runsvdir: DisplayService I : dpvsync : 60.33
Jan 25 00:01:24 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:24 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:24 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:24 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:24 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:24 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:24 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:24 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:24 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:24 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:24 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:24 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:24 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:24 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:24 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:24 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:24 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:24 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:24 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:24 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:24 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:24 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:24 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:24 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:24 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:24 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:24 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:01:24 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:01:25 runsvdir: DisplaySerALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:25 runsvdir: [audio service]:try_again
Jan 25 00:01:28 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:28 runsvdir: [audio service]:try_again
Jan 25 00:01:28 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:28 runsvdir: [audio service]:try_again
Jan 25 00:01:28 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:28 runsvdir: [audio service]:try_again
Jan 25 00:01:29 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:29 runsvdir: [audio service]:try_again
Jan 25 00:01:29 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:29 runsvdir: [audio service]:try_again
Jan 25 00:01:34 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:34 runsvdir: [audio service]:try_again
Jan 25 00:01:44 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:44 runsvdir: [audio service]:try_again
Jan 25 00:01:47 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:47 runsvdir: [audio service]:try_again
Jan 25 00:01:48 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:48 runsvdir: [audio service]:try_again
Jan 25 00:01:48 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:48 runsvdir: [audio service]:try_again
Jan 25 00:01:49 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:49 runsvdir: [audio service]:try_again
Jan 25 00:01:49 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:49 runsvdir: [audio service]:try_again
Jan 25 00:01:50 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:50 runsvdir: [audio service]:try_again
Jan 25 00:01:50 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:50 runsvdir: [audio service]:try_again
Jan 25 00:01:51 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:51 runsvdir: [audio service]:try_again
Jan 25 00:01:52 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:52 runsvdir: [audio service]:try_again
Jan 25 00:01:52 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:52 runsvdir: [audio service]:try_again
Jan 25 00:01:53 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:53 runsvdir: [audio service]:try_again
Jan 25 00:01:54 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:54 runsvdir: [audio service]:try_again
Jan 25 00:01:54 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:54 runsvdir: [audio service]:try_again
Jan 25 00:01:54 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:54 runsvdir: [audio service]:try_again
Jan 25 00:01:54 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:54 runsvdir: [audio service]:try_again
Jan 25 00:01:54 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:54 runsvdir: [audio service]:try_again
Jan 25 00:01:55 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:55 runsvdir: [audio service]:try_again
Jan 25 00:01:56 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:56 runsvdir: [audio service]:try_again
Jan 25 00:01:56 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:56 runsvdir: [audio service]:try_again
Jan 25 00:01:56 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:56 runsvdir: [audio service]:try_again
Jan 25 00:01:56 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:56 runsvdir: [audio service]:try_again
Jan 25 00:01:56 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:56 runsvdir: [audio service]:try_again
Jan 25 00:01:56 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:56 runsvdir: [audio service]:try_again
Jan 25 00:01:57 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:57 runsvdir: [audio service]:try_again
Jan 25 00:01:58 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:58 runsvdir: [audio service]:try_again
Jan 25 00:01:58 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:58 runsvdir: [audio service]:try_again
Jan 25 00:01:58 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:01:58 runsvdir: [audio service]:try_again
Jan 25 00:02:00 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:00 runsvdir: [audio service]:try_again
Jan 25 00:02:00 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:00 runsvdir: [audio service]:try_again
Jan 25 00:02:01 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:01 runsvdir: [audio service]:try_again
Jan 25 00:02:01 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:01 runsvdir: [audio service]:try_again
Jan 25 00:02:03 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:03 runsvdir: [audio service]:try_again
Jan 25 00:02:03 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:03 runsvdir: [audio service]:try_again
Jan 25 00:02:04 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:04 runsvdir: [audio service]:try_again
Jan 25 00:02:04 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:04 runsvdir: [audio service]:try_again
Jan 25 00:02:05 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:05 runsvdir: [audio service]:try_again
Jan 25 00:02:06 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:06 runsvdir: [audio service]:try_again
Jan 25 00:02:06 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:06 runsvdir: [audio service]:try_again
Jan 25 00:02:07 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:07 runsvdir: [audio service]:try_again
Jan 25 00:02:07 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:07 runsvdir: [audio service]:try_again
Jan 25 00:02:07 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:07 runsvdir: [audio service]:try_again
Jan 25 00:02:08 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:08 runsvdir: [audio service]:try_again
Jan 25 00:02:08 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:08 runsvdir: [audio service]:try_again
Jan 25 00:02:09 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:09 runsvdir: [audio service]:try_again
Jan 25 00:02:10 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:10 runsvdir: [audio service]:try_again
Jan 25 00:02:10 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:10 runsvdir: [audio service]:try_again
Jan 25 00:02:11 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:11 runsvdir: [audio service]:try_again
Jan 25 00:02:16 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:16 runsvdir: [audio service]:try_again
Jan 25 00:02:25 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:25 runsvdir: [audio service]:try_again
Jan 25 00:02:27 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:27 runsvdir: [audio service]:try_again
Jan 25 00:02:27 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:27 runsvdir: [audio service]:try_again
Jan 25 00:02:27 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:27 runsvdir: [audio service]:try_again
Jan 25 00:02:29 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:29 runsvdir: [audio service]:try_again
Jan 25 00:02:30 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:30 runsvdir: [audio service]:try_again
Jan 25 00:02:32 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:32 runsvdir: [audio service]:try_again
Jan 25 00:02:34 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:34 runsvdir: [audio service]:try_again
Jan 25 00:02:34 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:34 runsvdir: [audio service]:try_again
Jan 25 00:02:35 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:35 runsvdir: [audio service]:try_again
Jan 25 00:02:35 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:35 runsvdir: [audio service]:try_again
Jan 25 00:02:35 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:35 runsvdir: [audio service]:try_again
Jan 25 00:02:35 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:35 runsvdir: [audio service]:try_again
Jan 25 00:02:36 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:36 runsvdir: [audio service]:try_again
Jan 25 00:02:37 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:37 runsvdir: [audio service]:try_again
Jan 25 00:02:39 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:39 runsvdir: [audio service]:try_again
Jan 25 00:02:42 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:42 runsvdir: [audio service]:try_again
Jan 25 00:02:43 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:43 runsvdir: [audio service]:try_again
Jan 25 00:02:44 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:44 runsvdir: [audio service]:try_again
Jan 25 00:02:47 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:47 runsvdir: [audio service]:try_again
Jan 25 00:02:52 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:52 runsvdir: [audio service]:try_again
Jan 25 00:02:53 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:53 runsvdir: [audio service]:try_again
Jan 25 00:02:53 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:53 runsvdir: [audio service]:try_again
Jan 25 00:02:53 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:53 runsvdir: [audio service]:try_again
Jan 25 00:02:53 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:53 runsvdir: [audio service]:try_again
Jan 25 00:02:55 runsvdir: [audio service]:[audio]:handleClientRequest cmd[7]
Jan 25 00:02:55 runsvdir: [audio service]:[audio binder service]:GET_CURRENT_MODE GET_CURRENT_MODE[0]
Jan 25 00:02:58 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:58 runsvdir: [audio service]:try_again
Jan 25 00:02:59 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:02:59 runsvdir: [audio service]:try_again
Jan 25 00:03:01 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:01 runsvdir: [audio service]:try_again
Jan 25 00:03:04 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:04 runsvdir: [audio service]:try_again
Jan 25 00:03:05 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:05 runsvdir: [audio service]:try_again
Jan 25 00:03:05 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:05 runsvdir: [audio service]:try_again
Jan 25 00:03:05 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:05 runsvdir: [audio service]:try_again
Jan 25 00:03:06 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:06 runsvdir: [audio service]:try_again
Jan 25 00:03:07 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:07 runsvdir: [audio service]:try_again
Jan 25 00:03:07 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:07 runsvdir: [audio service]:try_again
Jan 25 00:03:17 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:17 runsvdir: [audio service]:try_again
Jan 25 00:03:18 runsvdir: vice I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:18 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:18 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:18 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:18 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:18 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:18 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:18 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:18 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:18 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:18 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:18 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:18 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:18 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:18 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:18 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:18 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:18 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:18 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:18 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:18 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:18 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:18 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:18 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:18 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:18 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:18 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:18 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:18 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:18 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:18 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:18 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:18 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:18 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:18 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:18 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:18 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:18 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:18 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:18 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:18 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:18 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:18 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:18 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:18 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:18 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:18 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:18 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:18 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:18 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:18 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:18 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:18 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:18 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:18 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:18 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:18 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:18 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:18 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:18 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:18 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:18 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:18 runsvdir: DisplayService I : check oled status:open, ret = 0
Jan 25 00:03:18 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:18 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:18 runsvdir: DisplayService I : Get frame stats: Successes 60.33, Failures 0.00
Jan 25 00:03:18 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:18 runsvdir: DisplayService I : Get frame stats: Successes 59.67, Failures 0.00
Jan 25 00:03:18 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:18 runsvdir: DisplayService I : get edid 5, dp_audio 0
Jan 25 00:03:18 runsvdir: DisplayService I : current vo's refresh rate 90Hz, return 3
Jan 25 00:03:18 runsvdir: DisplayService I : Get frame stats: Successes 60.33, Failures 0.00
Jan 25 00:03:18 runsvdir: DisplayService I : dpvsync : 60.33
Jan 25 00:03:18 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:18 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:18 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:18 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:18 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:18 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:03:18 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:03:24 runsvdir: DisplaySerALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:24 runsvdir: [audio service]:try_again
Jan 25 00:03:25 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:25 runsvdir: [audio service]:try_again
Jan 25 00:03:28 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:28 runsvdir: [audio service]:try_again
Jan 25 00:03:28 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:28 runsvdir: [audio service]:try_again
Jan 25 00:03:29 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:29 runsvdir: [audio service]:try_again
Jan 25 00:03:29 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:29 runsvdir: [audio service]:try_again
Jan 25 00:03:30 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:30 runsvdir: [audio service]:try_again
Jan 25 00:03:30 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:30 runsvdir: [audio service]:try_again
Jan 25 00:03:30 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:30 runsvdir: [audio service]:try_again
Jan 25 00:03:31 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:31 runsvdir: [audio service]:try_again
Jan 25 00:03:31 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:31 runsvdir: [audio service]:try_again
Jan 25 00:03:37 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:37 runsvdir: [audio service]:try_again
Jan 25 00:03:38 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:38 runsvdir: [audio service]:try_again
Jan 25 00:03:38 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:38 runsvdir: [audio service]:try_again
Jan 25 00:03:38 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:38 runsvdir: [audio service]:try_again
Jan 25 00:03:39 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:39 runsvdir: [audio service]:try_again
Jan 25 00:03:39 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:39 runsvdir: [audio service]:try_again
Jan 25 00:03:40 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:40 runsvdir: [audio service]:try_again
Jan 25 00:03:41 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:41 runsvdir: [audio service]:try_again
Jan 25 00:03:41 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:41 runsvdir: [audio service]:try_again
Jan 25 00:03:41 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:41 runsvdir: [audio service]:try_again
Jan 25 00:03:41 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:41 runsvdir: [audio service]:try_again
Jan 25 00:03:43 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:43 runsvdir: [audio service]:try_again
Jan 25 00:03:43 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:43 runsvdir: [audio service]:try_again
Jan 25 00:03:43 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:43 runsvdir: [audio service]:try_again
Jan 25 00:03:44 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:44 runsvdir: [audio service]:try_again
Jan 25 00:03:45 runsvdir: EY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:03:45 runsvdir: ^[[35;22m[124391446][0300][0x7f8083c200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:03:45 runsvdir: 374d7000 37911000 37a21000 0 0 0 37b2b000 37f65000 38075000 0 0 0 
Jan 25 00:03:45 runsvdir: ^[[35;22m[124391448][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:03:45 runsvdir: ^[[35;22m[124391450][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:03:45 runsvdir: ^[[35;22m[124391462][0300][0x7f813ae200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:03:45 runsvdir: ^[[35;22m[124391463][0300][0x7f813ae200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:03:45 runsvdir: ^[[31;22m[124391464][0498][0x7ef28781a0][VPSS_MPP][ERROR] mpi_vpss.c: ar_hal_vpss_create : 870 bind_register_sender failed 0x80028003^[[0m
Jan 25 00:03:45 runsvdir: ^[[33;22m[124391464][0300][0x7f80a45200][VO_CORE][WARN] display_api.c: dc_init : 2258 use dc_reg_irq_by_cmd!^[[0m
Jan 25 00:03:45 runsvdir: ^[[31;22m[124391464][0300][0x7f80a45200][VO_CORE][ERROR] display_top.c: ar_display_dev_enable : 3521 not support set irq_type:^[[33;22m[124391465][0300][0x7f80a45200][VO_CORE][WARN] display_api.c: dc_init : 2258 use dc_reg_irq_
Jan 25 00:03:45 runsvdir: ^[[33;22m[124391465][0300][0x7f813ae200][AXIMGR_CORE][WARN] ar_aximgr_lb.c: ar_aximgr_lb_enable : 285 [LB] 1920 1024 1024^[[0m
Jan 25 00:03:45 runsvdir: ^[[33;22m[124391465][0300][0x7f80a45200][AXIMGR_CORE][WARN] ar_aximgr_lb.c: ar_aximgr_lb_enable : 285 [LB] 1920 1024 1024^[[0m
Jan 25 00:03:45 runsvdir: ^[[35;22m[124391465][0300][0x7f8083c200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:03:45 runsvdir: ^[[35;22m[124391465][0300][0x7f8083c200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:03:45 runsvdir: ^[[35;22m[124391466][0300][0x7f8081b200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:03:45 runsvdir: ^[[35;22m[124391466][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:03:45 runsvdir: 36e7d000 372b7000 373c7000 0 0 0 374d1000 3790b000 37a1b000 0 0 0 
Jan 25 00:03:45 runsvdir: ^[[35;22m[124391466][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:03:45 runsvdir: ^[[35;22m[124391466][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:03:45 runsvdir: aec index 198685398 line 1125 gain 1.000000 
Jan 25 00:03:45 runsvdir: ^[[35;22m[124391466][0300][0x7f8081b200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:03:45 runsvdir: ^[[35;22m[124391466][0300][0x7f8081b200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:03:45 runsvdir: 374d7000 37911000 37a21000 0 0 0 37b2b000 37f65000 38075000 0 0 0 
Jan 25 00:03:45 runsvdir: ^[[35;22m[124391468][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:03:45 runsvdir: ^[[35;22m[124391470][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:03:45 runsvdir: ^[[33;22m[124396937][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:03:45 runsvdir: ^[[0m
Jan 25 00:03:45 runsvdir: ^[[33;22m[124398697][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:03:45 runsvdir: ^[[0m
Jan 25 00:03:45 runsvdir: ^[[33;22m[124400004][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:03:45 runsvdir: ^[[0m
Jan 25 00:03:45 runsvdir: ^[[33;22m[124404986][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:03:45 runsvdir: ^[[0m
Jan 25 00:03:45 runsvdir: ^[[33;22m[124406406][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:03:45 runsvdir: ^[[0m
Jan 25 00:03:45 runsvdir: ^[[33;22m[124408020][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:03:45 runsvdir: ^[[0m
Jan 25 00:03:45 runsvdir: ^[[33;22m[124408046][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:03:45 runsvdir: ^[[0m
Jan 25 00:03:45 runsvdir: ^[[33;22m[124411214][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:03:45 runsvdir: ^[[0m
Jan 25 00:03:45 runsvdir: ^[[33;22m[124411994][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:03:45 runsvdir: ^[[0m
Jan 25 00:03:45 runsvdir: ^[[33;22m[124412814][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: arALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:45 runsvdir: [audio service]:try_again
Jan 25 00:03:45 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:45 runsvdir: [audio service]:try_again
Jan 25 00:03:45 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:45 runsvdir: [audio service]:try_again
Jan 25 00:03:45 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:45 runsvdir: [audio service]:try_again
Jan 25 00:03:45 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:45 runsvdir: [audio service]:try_again
Jan 25 00:03:45 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:45 runsvdir: [audio service]:try_again
Jan 25 00:03:46 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:46 runsvdir: [audio service]:try_again
Jan 25 00:03:46 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:46 runsvdir: [audio service]:try_again
Jan 25 00:03:46 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:46 runsvdir: [audio service]:try_again
Jan 25 00:03:46 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:46 runsvdir: [audio service]:try_again
Jan 25 00:03:46 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:46 runsvdir: [audio service]:try_again
Jan 25 00:03:46 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:46 runsvdir: [audio service]:try_again
Jan 25 00:03:46 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:46 runsvdir: [audio service]:try_again
Jan 25 00:03:46 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:46 runsvdir: [audio service]:try_again
Jan 25 00:03:47 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:47 runsvdir: [audio service]:try_again
Jan 25 00:03:47 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:47 runsvdir: [audio service]:try_again
Jan 25 00:03:47 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:47 runsvdir: [audio service]:try_again
Jan 25 00:03:47 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:47 runsvdir: [audio service]:try_again
Jan 25 00:03:48 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:48 runsvdir: [audio service]:try_again
Jan 25 00:03:48 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:48 runsvdir: [audio service]:try_again
Jan 25 00:03:48 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:48 runsvdir: [audio service]:try_again
Jan 25 00:03:49 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:49 runsvdir: [audio service]:try_again
Jan 25 00:03:52 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:52 runsvdir: [audio service]:try_again
Jan 25 00:03:52 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:52 runsvdir: [audio service]:try_again
Jan 25 00:03:52 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:52 runsvdir: [audio service]:try_again
Jan 25 00:03:54 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:54 runsvdir: [audio service]:try_again
Jan 25 00:03:54 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:54 runsvdir: [audio service]:try_again
Jan 25 00:03:54 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:54 runsvdir: [audio service]:try_again
Jan 25 00:03:54 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:54 runsvdir: [audio service]:try_again
Jan 25 00:03:54 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:54 runsvdir: [audio service]:try_again
Jan 25 00:03:54 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:54 runsvdir: [audio service]:try_again
Jan 25 00:03:55 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:55 runsvdir: [audio service]:try_again
Jan 25 00:03:56 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:56 runsvdir: [audio service]:try_again
Jan 25 00:03:56 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:03:56 runsvdir: [audio service]:try_again
Jan 25 00:04:00 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:00 runsvdir: [audio service]:try_again
Jan 25 00:04:02 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:02 runsvdir: [audio service]:try_again
Jan 25 00:04:03 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:03 runsvdir: [audio service]:try_again
Jan 25 00:04:03 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:03 runsvdir: [audio service]:try_again
Jan 25 00:04:05 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:05 runsvdir: [audio service]:try_again
Jan 25 00:04:05 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:05 runsvdir: [audio service]:try_again
Jan 25 00:04:05 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:05 runsvdir: [audio service]:try_again
Jan 25 00:04:05 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:05 runsvdir: [audio service]:try_again
Jan 25 00:04:05 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:05 runsvdir: [audio service]:try_again
Jan 25 00:04:08 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:08 runsvdir: [audio service]:try_again
Jan 25 00:04:09 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:09 runsvdir: [audio service]:try_again
Jan 25 00:04:09 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:09 runsvdir: [audio service]:try_again
Jan 25 00:04:09 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:09 runsvdir: [audio service]:try_again
Jan 25 00:04:09 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:09 runsvdir: [audio service]:try_again
Jan 25 00:04:09 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:09 runsvdir: [audio service]:try_again
Jan 25 00:04:10 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:10 runsvdir: [audio service]:try_again
Jan 25 00:04:12 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:12 runsvdir: [audio service]:try_again
Jan 25 00:04:12 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:12 runsvdir: [audio service]:try_again
Jan 25 00:04:12 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:12 runsvdir: [audio service]:try_again
Jan 25 00:04:12 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:12 runsvdir: [audio service]:try_again
Jan 25 00:04:12 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:12 runsvdir: [audio service]:try_again
Jan 25 00:04:12 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:12 runsvdir: [audio service]:try_again
Jan 25 00:04:12 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:12 runsvdir: [audio service]:try_again
Jan 25 00:04:13 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:13 runsvdir: [audio service]:try_again
Jan 25 00:04:14 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:14 runsvdir: [audio service]:try_again
Jan 25 00:04:14 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:14 runsvdir: [audio service]:try_again
Jan 25 00:04:16 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:16 runsvdir: [audio service]:try_again
Jan 25 00:04:16 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:16 runsvdir: [audio service]:try_again
Jan 25 00:04:16 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:16 runsvdir: [audio service]:try_again
Jan 25 00:04:16 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:16 runsvdir: [audio service]:try_again
Jan 25 00:04:18 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:18 runsvdir: [audio service]:try_again
Jan 25 00:04:21 runsvdir: vice I : dpvsync : 60.00
Jan 25 00:04:21 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:04:21 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:04:21 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:04:21 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:04:21 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:04:21 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:04:21 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:04:21 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:04:21 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:04:21 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:04:21 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:04:21 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:04:21 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:04:21 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:04:21 runsvdir: DisplayService I : Get frame stats: Successes 60.33, Failures 0.00
Jan 25 00:04:21 runsvdir: DisplayService I : dpvsync : 60.33
Jan 25 00:04:21 runsvdir: DisplayService I : Get frame stats: Successes 59.67, Failures 0.00
Jan 25 00:04:21 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:04:21 runsvdir: DisplayService I : Get frame stats: Successes 60.33, Failures 0.00
Jan 25 00:04:21 runsvdir: DisplayService I : dpvsync : 59.67
Jan 25 00:04:21 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:04:21 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:04:21 runsvdir: DisplayService I : Get frame stats: Successes 59.67, Failures 0.00
Jan 25 00:04:21 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:04:21 runsvdir: DisplayService I : Get frame stats: Successes 60.33, Failures 0.00
Jan 25 00:04:21 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:04:21 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:04:21 runsvdir: DisplayService I : dpvsync : 60.33
Jan 25 00:04:21 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:04:21 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:04:21 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:04:21 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:04:21 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:04:21 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:04:21 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:04:21 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:04:21 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:04:21 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:04:21 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:04:21 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:04:21 runsvdir: DisplayService I : get dp info 3840x1080@60
Jan 25 00:04:21 runsvdir: DisplayService I : check oled status:open, ret = 0
Jan 25 00:04:21 runsvdir: DisplayService I : set duty 0
Jan 25 00:04:21 runsvdir: DisplayService I : <timestamp open/close oled> -- [ 261.782 ]
Jan 25 00:04:21 runsvdir: DisplayService I : Screen successfully closed.
Jan 25 00:04:21 runsvdir: DisplayService I : get dp info 3840x1080@60
Jan 25 00:04:21 runsvdir: DisplayService I : get a cmd: stop dpisp
Jan 25 00:04:21 runsvdir: DisplayService I : dpisp get a new state:0
Jan 25 00:04:21 runsvdir: DisplayService I : <timestamp DPSIP stop start> -- [ 262.265 ]
Jan 25 00:04:21 runsvdir: DisplayService I : <timestamp force release vb done> -- [ 262.265 ]
Jan 25 00:04:21 runsvdir: DisplayService I : free vb count = 8, inused vb count = 0
Jan 25 00:04:21 runsvdir: DisplayService I : <timestamp DPSIP stop done> -- [ 262.273 ]
Jan 25 00:04:21 runsvdir: DisplayService D : destory pipi = 2, isp object = 0x7f91f88108
Jan 25 00:04:21 runsvdir: DisplayService I : <timestamp destory vi done> -- [ 262.273 ]
Jan 25 00:04:21 runsvdir: DisplayService I : current dpisp status update :0
Jan 25 00:04:21 runsvdir: DisplayOLED I : screen soft in change refresh reset successfully!
Jan 25 00:04:21 runsvdir: DisplayOLED I : OLED no set force lane!
Jan 25 00:04:21 runsvdir: DisplayOLED I : GF screen set inverse scan direction
Jan 25 00:04:21 runsvdir: DisplayService I : Configure OLED to 5lanesX2@90Hz Success
Jan 25 00:04:21 runsvdir: DisplayService I : get cmd CMD_VI_RESUME_BY_VO
Jan 25 00:04:21 runsvdir: DisplayService I : dpisp get a new state:1
Jan 25 00:04:21 runsvdir: DisplayService D : get dprx so
Jan 25 00:04:21 runsvdir: DisplayService D : init pipi = 2, isp object = 0x7f91f88108
Jan 25 00:04:21 runsvdir: DisplayService I : enPixelFormat:0x17
Jan 25 00:04:21 runsvdir: DisplayService I : get dpvif_fre_hz = 333000000
Jan 25 00:04:21 runsvdir: DisplayService I : >>>>>> stComboAttr >>>>>>>
Jan 25 00:04:21 runsvdir: DisplayService I : devno: 0
Jan 25 00:04:21 runsvdir: DisplayService I : input_mode: 0x0b
Jan 25 00:04:21 runsvdir: DisplayService I : data_rate : 450
Jan 25 00:04:21 runsvdir: DisplayService I : mipi-input_data_type: 0x02
Jan 25 00:04:21 runsvdir: DisplayService I : stSnsSize.u32Width = 3840
Jan 25 00:04:21 runsvdir: DisplayService I : stSnsSize.u32Height = 1080
Jan 25 00:04:21 runsvdir: DisplayService I : stTiming.hblank = 560
Jan 25 00:04:21 runsvdir: DisplayService I : stTiming.vblank = 45
Jan 25 00:04:21 runsvdir: DisplayService I : stChnAttr.stSize.u32Width = 3840
Jan 25 00:04:21 runsvdir: DisplayService I : stChnAttr.stSize.u32Height = 1080
Jan 25 00:04:21 runsvdir: DisplayService I : stChnAttr.u32BufCount = 8
Jan 25 00:04:21 runsvdir: DisplayService I : stPubAttr.f32FrameRate = 60.000000
Jan 25 00:04:21 runsvdir: DisplayService I : <<<<<< stComboAttr <<<<<<<
Jan 25 00:04:21 runsvdir: read vb config data index:4 success.
Jan 25 00:04:21 runsvdir: DisplayService I : pipe:2 channel:0 get vb pool id <4>!
Jan 25 00:04:21 runsvdir: read vb config data index:4 success.
Jan 25 00:04:21 runsvdir: get vb pool id_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:04:21 runsvdir: ^[[0m
Jan 25 00:04:21 runsvdir: ^[[33;22m[124413621][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:04:21 runsvdir: ^[[0m
Jan 25 00:04:21 runsvdir: ^[[33;22m[124413755][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:04:21 runsvdir: ^[[0m
Jan 25 00:04:21 runsvdir: ^[[33;22m[124414165][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:04:21 runsvdir: ^[[0m
Jan 25 00:04:21 runsvdir: ^[[33;22m[124414271][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:04:21 runsvdir: ^[[0m
Jan 25 00:04:21 runsvdir: ^[[33;22m[124414282][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:04:21 runsvdir: ^[[0m
Jan 25 00:04:21 runsvdir: ^[[33;22m[124414535][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:04:21 runsvdir: ^[[0m
Jan 25 00:04:21 runsvdir: ^[[33;22m[124414545][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:04:21 runsvdir: ^[[0m
Jan 25 00:04:21 runsvdir: ^[[33;22m[124415132][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:04:21 runsvdir: ^[[0m
Jan 25 00:04:21 runsvdir: ^[[33;22m[124415329][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:04:21 runsvdir: ^[[0m
Jan 25 00:04:21 runsvdir: ^[[33;22m[124415632][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:04:21 runsvdir: ^[[0m
Jan 25 00:04:21 runsvdir: ^[[33;22m[124416480][0300][0x7f8081b200][VO_CORE][WARN] display_api.c: dc_deinit : 2323 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:04:21 runsvdir: ^[[33;22m[124416480][0300][0x7f813ae200][VO_CORE][WARN] display_api.c: dc_deinit : 2323 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:04:21 runsvdir: ^[[35;22m[124416480][0300][0x7f80759200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:04:21 runsvdir: ^[[35;22m[124416480][0300][0x7f80759200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:04:21 runsvdir: ^[[35;22m[124416480][0300][0x7f82382200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_close_camera : 961 camera_id=1^[[0m
Jan 25 00:04:21 runsvdir: open the driver sns:dp_rx obj:stSnsdp_rxObj lib:libsns_dp_rx.so 
Jan 25 00:04:21 runsvdir: ^[[31;22m[25 00:04:21.854423][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3559 !!!!!!the vif is force to dpcm duce to reduce bandwidth, if you want to use the dng, please set the pstPipeAttr->enEnableN
Jan 25 00:04:21 runsvdir: ^[[31;22m[25 00:04:21.854498][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3603 !!!!!!! Note: your app cfg the vif write compress on,so you must cfg the vif fre >=3*mipi fre !!!!!!^[[0m
Jan 25 00:04:21 runsvdir: ^[[31;22m[25 00:04:21.854742][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7846 not supported addr type^[[0m
Jan 25 00:04:21 runsvdir: ^[[31;22m[25 00:04:21.854758][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7856 not supported data type^[[0m
Jan 25 00:04:21 runsvdir: ^[[35;22m[124416483][0300][0x7f80738200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_open_camera : 920 camera_id=1^[[0m
Jan 25 00:04:21 runsvdir: ^[[35;22m[124416483][0300][0x7f806f6200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:04:21 runsvdir: ^[[35;22m[124416483][0300][0x7f806f6200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:04:21 runsvdir: ^[[35;22m[124416483][0300][0x7f812a9200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:04:21 runsvdir: ^[[35;22m[124416483][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:04:21 runsvdir: 36e7d000 372b7000 373c7000 0 0 0 374d1000 3790b000 37a1b000 0 0 0 
Jan 25 00:04:21 runsvdir: ^[[35;22m[124416483][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:04:21 runsvdir: ^[[35;22m[124416483][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:04:21 runsvdir: aec index 198685398 line 1125 gain 1.000000 
Jan 25 00:04:21 runsvdir: ^[[35;22m[124416484][0300][0x7f812a9200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:04:21 runsvdir: ^[[35;22m[124416484][0300][0x7f812a9200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:04:21 runsvdir: 374d7000 37911000 37a21000 0 0 0 37b2b000 37f65000 38075000 0 0 0 
Jan 25 00:04:21 runsvdir: ^[[35;22m[124416486][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_iALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:21 runsvdir: [audio service]:try_again
Jan 25 00:04:22 runsvdir: d=1^[[0m
Jan 25 00:04:22 runsvdir: ^[[35;22m[124416487][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:04:22 runsvdir: ^[[35;22m[124416500][0300][0x7f81267200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:04:22 runsvdir: ^[[35;22m[124416500][0300][0x7f81267200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:04:22 runsvdir: ^[[31;22m[124416503][0498][0x7ef28781a0][VPSS_MPP][ERROR] mpi_vpss.c: ar_hal_vpss_create : 870 bind_register_sender failed 0x80028003^[[0m
Jan 25 00:04:22 runsvdir: ^[[33;22m[124416503][0300][0x7f80759200][VO_CORE][WARN] display_api.c: dc_init : 2258 use dc_reg_irq_by_cmd!^[[0m
Jan 25 00:04:22 runsvdir: ^[[31;22m[124416503][0300][0x7f80759200][VO_CORE][ERROR] display_top.c: ar_display_dev_enable : 3521 not support set irq_type:^[[33;22m[124416503][0300][0x7f812a9200][VO_CORE][WARN] display_api.c: dc_init : 2258 use dc_reg_irq_
Jan 25 00:04:22 runsvdir: ^[[33;22m[124416503][0300][0x7f81267200][AXIMGR_CORE][WARN] ar_aximgr_lb.c: ar_aximgr_lb_enable : 285 [LB] 1920 1024 1024^[[0m
Jan 25 00:04:22 runsvdir: ^[[33;22m[124416503][0300][0x7f80759200][AXIMGR_CORE][WARN] ar_aximgr_lb.c: ar_aximgr_lb_enable : 285 [LB] 1920 1024 1024^[[0m
Jan 25 00:04:22 runsvdir: ^[[35;22m[124416503][0300][0x7f82382200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:04:22 runsvdir: ^[[35;22m[124416503][0300][0x7f82382200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:04:22 runsvdir: ^[[35;22m[124416504][0300][0x7f80759200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:04:22 runsvdir: ^[[35;22m[124416504][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:04:22 runsvdir: 36e7d000 372b7000 373c7000 0 0 0 374d1000 3790b000 37a1b000 0 0 0 
Jan 25 00:04:22 runsvdir: 374d7000 37911000 37a21000 0 0 0 37b2b000 37f65000 38075000 0 0 0 
Jan 25 00:04:22 runsvdir: ^[[35;22m[124416504][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:04:22 runsvdir: ^[[35;22m[124416504][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:04:22 runsvdir: aec index 198685398 line 1125 gain 1.000000 
Jan 25 00:04:22 runsvdir: ^[[35;22m[124416504][0300][0x7f80759200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:04:22 runsvdir: ^[[35;22m[124416504][0300][0x7f80759200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:04:22 runsvdir: ^[[35;22m[124416506][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:04:22 runsvdir: ^[[35;22m[124416507][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:04:22 runsvdir: ^[[33;22m[124416510][0300][0x7f80738200][VO_CORE][WARN] display_api.c: dc_deinit : 2323 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:04:22 runsvdir: ^[[33;22m[124416510][0300][0x7f808e1200][VO_CORE][WARN] display_api.c: dc_deinit : 2323 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:04:22 runsvdir: ^[[35;22m[124416510][0300][0x7f82382200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:04:22 runsvdir: ^[[35;22m[124416510][0300][0x7f82382200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:04:22 runsvdir: ^[[35;22m[124416511][0300][0x7f80759200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_close_camera : 961 camera_id=1^[[0m
Jan 25 00:04:22 runsvdir: open the driver sns:dp_rx obj:stSnsdp_rxObj lib:libsns_dp_rx.so 
Jan 25 00:04:22 runsvdir: ^[[31;22m[25 00:04:22.158570][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3559 !!!!!!the vif is force to dpcm duce to reduce bandwidth, if you want to use the dng, please set the pstPipeAttr->enEnableN
Jan 25 00:04:22 runsvdir: ^[[31;22m[25 00:04:22.158601][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3603 !!!!!!! Note: your app cfg the vif write compress on,so you must cfg the vif fre >=3*mipi fre !!!!!!^[[0m
Jan 25 00:04:22 runsvdir: ^[[31;22m[25 00:04:22.158744][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7846 not supported addr type^[[0m
Jan 25 00:04:22 runsvdir: ^[[31;22m[25 00:04:22.158757][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7856 not supported data type^[[0m
Jan 25 00:04:22 runsvdir: ^[[35;22m[124416513][0300][0x7f8081b200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_open_camera : 920 camera_id=1^[[0m
Jan 25 00:04:22 runsvdir: ^[[35;22m[124416513][0300][0x7f80738200][SYS_^[[0;31mDisplayService E: screen_get_duty[274] Failed to get duty. Error: -2
Jan 25 00:04:22 runsvdir: ^[[0;39m
Jan 25 00:04:22 runsvdir:  <4>!
Jan 25 00:04:22 runsvdir: DisplayService I : pipe:2 chn:0 bind vb pool:4 success!
Jan 25 00:04:22 runsvdir: DisplayService I : <timestamp AR_MPI_VI_GetHightPricisionPipeFPS start> -- [ 262.303 ]
Jan 25 00:04:22 runsvdir: DisplayService I : Get frame stats: Successes 47.00, Failures 0.00
Jan 25 00:04:22 runsvdir: DisplayService I : dpvsync : 59.33
Jan 25 00:04:22 runsvdir: DisplayService I : get high pricision fps = 60.002216
Jan 25 00:04:22 runsvdir: DisplayService I : <timestamp AR_MPI_VI_GetHightPricisionPipeFPS end> -- [ 262.464 ]
Jan 25 00:04:22 runsvdir: DisplayService I : isp attr: type : bt709, color range : full range
Jan 25 00:04:22 runsvdir: DisplayService I : <timestamp init vi done> -- [ 262.469 ]
Jan 25 00:04:22 runsvdir: DisplayService I : current dpisp status update :1
Jan 25 00:04:22 runsvdir: DisplayService I : get a cmd: start dpisp
Jan 25 00:04:22 runsvdir: DisplayService I : dpisp get a new state:2
Jan 25 00:04:22 runsvdir: DisplayService I : only enable dpisp
Jan 25 00:04:22 runsvdir: DisplayService I : <timestamp init isp > -- [ 262.501 ]
Jan 25 00:04:22 runsvdir: DisplayService I : enable vi channel output
Jan 25 00:04:22 runsvdir: DisplayService I : check oled status:close, ret = 0
Jan 25 00:04:22 runsvdir: DisplayService I : set duty 0
Jan 25 00:04:22 runsvdir: DisplayService I : Setting new LUT for mode: dp_scaler_lut_direct_mode_1
Jan 25 00:04:22 runsvdir: DisplayService I : <timestamp init isp done> -- [ 262.511 ]
Jan 25 00:04:22 runsvdir: DisplayService I : current dpisp status update :2
Jan 25 00:04:22 runsvdir: DisplayService I : <timestamp open/close oled> -- [ 262.523 ]
Jan 25 00:04:22 runsvdir: DisplayService I : Screen successfully opened.
Jan 25 00:04:22 runsvdir: DisplayService I : set duty 30
Jan 25 00:04:22 runsvdir: DisplayService I : check oled status:open, ret = 0
Jan 25 00:04:22 runsvdir: DisplayService I : set duty 30
Jan 25 00:04:22 runsvdir: DisplayService I : get dp info 3840x1080@60
Jan 25 00:04:22 runsvdir: DisplayService W : can't find node 0x7f8c002be0, ignore this release
Jan 25 00:04:22 runsvdir: DisplayService I : get dp info 3840x1080@60
Jan 25 00:04:22 runsvdir: DisplayService I : check oled status:open, ret = 0
Jan 25 00:04:22 runsvdir: DisplayService I : set duty 0
Jan 25 00:04:22 runsvdir: DisplayService I : <timestamp open/close oled> -- [ 262.560 ]
Jan 25 00:04:22 runsvdir: DisplayService I : Screen successfully closed.
Jan 25 00:04:22 runsvdir: DisplayService I : get dp info 3840x1080@60
Jan 25 00:04:22 runsvdir: DisplayService I : get a cmd: stop dpisp
Jan 25 00:04:22 runsvdir: DisplayService I : dpisp get a new state:0
Jan 25 00:04:22 runsvdir: DisplayService I : <timestamp DPSIP stop start> -- [ 262.569 ]
Jan 25 00:04:22 runsvdir: DisplayService I : <timestamp force release vb done> -- [ 262.569 ]
Jan 25 00:04:22 runsvdir: DisplayService I : free vb count = 8, inused vb count = 0
Jan 25 00:04:22 runsvdir: DisplayService I : <timestamp DPSIP stop done> -- [ 262.579 ]
Jan 25 00:04:22 runsvdir: DisplayService D : destory pipi = 2, isp object = 0x7f91f88108
Jan 25 00:04:22 runsvdir: DisplayService I : <timestamp destory vi done> -- [ 262.579 ]
Jan 25 00:04:22 runsvdir: DisplayService I : current dpisp status update :0
Jan 25 00:04:22 runsvdir: DisplayOLED I : screen soft in change refresh reset successfully!
Jan 25 00:04:22 runsvdir: DisplayOLED I : OLED no set force lane!
Jan 25 00:04:22 runsvdir: DisplayOLED I : GF screen set inverse scan direction
Jan 25 00:04:22 runsvdir: DisplayService I : Configure OLED to 5lanesX2@90Hz Success
Jan 25 00:04:22 runsvdir: DisplayService I : get cmd CMD_VI_RESUME_BY_VO
Jan 25 00:04:22 runsvdir: DisplayService I : dpisp get a new state:1
Jan 25 00:04:22 runsvdir: DisplayService D : get dprx so
Jan 25 00:04:22 runsvdir: DisplayService D : init pipi = 2, isp object = 0x7f91f88108
Jan 25 00:04:22 runsvdir: DisplayService I : enPixelFormat:0x17
Jan 25 00:04:22 runsvdir: DisplayService I : get dpvif_fre_hz = 333000000
Jan 25 00:04:22 runsvdir: DisplayService I : >>>>>> stComboAttr >>>>>>>
Jan 25 00:04:22 runsvdir: DisplayService I : devno: 0
Jan 25 00:04:22 runsvdir: DisplayService I : input_mode: 0x0b
Jan 25 00:04:22 runsvdir: DisplayService I : data_rate : 450
Jan 25 00:04:22 runsvdir: DisplayService I : mipi-input_data_type: 0x02
Jan 25 00:04:22 runsvdir: DisplayService I : stSnsSize.u32Width = 3840
Jan 25 00:04:22 runsvdir: DisplayService I : stSnsSize.u32Height = 1080
Jan 25 00:04:22 runsvdir: DisplayService I : stTiming.hblank = 560
Jan 25 00:04:22 runsvdir: DisplayService I : stTiming.vblank = 45
Jan 25 00:04:22 runsvdir: DisplayService I : stChnAttr.stSize.u32Width = 3840
Jan 25 00:04:22 runsvdir: DisplayService I : stChnAttr.stSize.u32Height = 1080
Jan 25 00:04:22 runsvdir: DisplayService I : stChnAttr.u32BufCount = 8
Jan 25 00:04:22 runsvdir: DisplayService I : stPubAttr.f32FrameRate = 60.000000
Jan 25 00:04:22 runsvdir: DisplayService I : <<<<<< stComboAttr <<<<<<<
Jan 25 00:04:22 runsvdir: read vb config data index:4 success.
Jan 25 00:04:22 runsvdir: DisplayService I : pipe:2 channel:0 get vb pool id <4>!
Jan 25 00:04:22 runsvdir: read vb config data index:4 success.
Jan 25 00:04:22 runsvdir: get vb pool id <4>!
Jan 25 00:04:22 runsvdir: DisplayService I : pipe:2 chn:0 bind vb pool:4 success!
Jan 25 00:04:22 runsvdir: DisplayService I : <timestamp AR_MPI_VI_GetHightPricisionPipeFPS start> -- [ 262.609 ]
Jan 25 00:04:22 runsvdir: DisplayService I : get high pricision fps = 60.002209
Jan 25 00:04:22 runsvdir: DisplayService I : <timestamp AR_MPI_VI_GetHightPricisionPipeFPS end> -- [ 262.770 ]
Jan 25 00:04:22 runsvdir: DisplayService I : isp attr: type : bt709, color range : full range
Jan 25 00:04:22 runsvdir: DisplayService I : <timestamp init vi done> -- [ 262.774 ]
Jan 25 00:04:22 runsvdir: DisplayService I : current dpisp status update :1
Jan 25 00:04:22 runsvdir: DisplayService I : get a cmd: start dpisp
Jan 25 00:04:22 runsvdir: DisplayService I : dpisp get a new state:2
Jan 25 00:04:22 runsvdir: DisplayService I : only enable dpisp
Jan 25 00:04:22 runsvdir: DisplayService I : <timestamp init isp > -- [ 262.805 ]
Jan 25 00:04:23 runsvdir: DisplayService I : enable vi chaALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:23 runsvdir: [audio service]:try_again
Jan 25 00:04:23 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:23 runsvdir: [audio service]:try_again
Jan 25 00:04:24 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:24 runsvdir: [audio service]:try_again
Jan 25 00:04:24 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:24 runsvdir: [audio service]:try_again
Jan 25 00:04:24 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:24 runsvdir: [audio service]:try_again
Jan 25 00:04:25 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:25 runsvdir: [audio service]:try_again
Jan 25 00:04:27 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:27 runsvdir: [audio service]:try_again
Jan 25 00:04:27 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:27 runsvdir: [audio service]:try_again
Jan 25 00:04:27 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:27 runsvdir: [audio service]:try_again
Jan 25 00:04:27 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:27 runsvdir: [audio service]:try_again
Jan 25 00:04:27 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:27 runsvdir: [audio service]:try_again
Jan 25 00:04:27 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:27 runsvdir: [audio service]:try_again
Jan 25 00:04:29 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:29 runsvdir: [audio service]:try_again
Jan 25 00:04:31 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:31 runsvdir: [audio service]:try_again
Jan 25 00:04:31 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:31 runsvdir: [audio service]:try_again
Jan 25 00:04:31 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:31 runsvdir: [audio service]:try_again
Jan 25 00:04:32 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:32 runsvdir: [audio service]:try_again
Jan 25 00:04:32 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:32 runsvdir: [audio service]:try_again
Jan 25 00:04:32 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:32 runsvdir: [audio service]:try_again
Jan 25 00:04:32 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:32 runsvdir: [audio service]:try_again
Jan 25 00:04:32 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:32 runsvdir: [audio service]:try_again
Jan 25 00:04:32 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:32 runsvdir: [audio service]:try_again
Jan 25 00:04:33 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:33 runsvdir: [audio service]:try_again
Jan 25 00:04:34 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:34 runsvdir: [audio service]:try_again
Jan 25 00:04:34 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:34 runsvdir: [audio service]:try_again
Jan 25 00:04:34 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:34 runsvdir: [audio service]:try_again
Jan 25 00:04:34 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:34 runsvdir: [audio service]:try_again
Jan 25 00:04:34 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:34 runsvdir: [audio service]:try_again
Jan 25 00:04:36 runsvdir: CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:04:36 runsvdir: ^[[35;22m[124416513][0300][0x7f80738200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:04:36 runsvdir: ^[[35;22m[124416514][0300][0x7f80759200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:04:36 runsvdir: ^[[35;22m[124416514][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:04:36 runsvdir: 36e7d000 372b7000 373c7000 0 0 0 374d1000 3790b000 37a1b000 0 0 0 
Jan 25 00:04:36 runsvdir: ^[[35;22m[124416514][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:04:36 runsvdir: ^[[35;22m[124416514][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:04:36 runsvdir: 374d7000 37911000 37a21000 0 0 0 37b2b000 37f65000 38075000 0 0 0 
Jan 25 00:04:36 runsvdir: aec index 198685398 line 1125 gain 1.000000 
Jan 25 00:04:36 runsvdir: ^[[35;22m[124416514][0300][0x7f80759200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:04:36 runsvdir: ^[[35;22m[124416514][0300][0x7f80759200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:04:36 runsvdir: ^[[35;22m[124416516][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:04:36 runsvdir: ^[[35;22m[124416517][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:04:36 runsvdir: ^[[35;22m[124416530][0300][0x7f8081b200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:04:36 runsvdir: ^[[35;22m[124416530][0300][0x7f8081b200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:04:36 runsvdir: ^[[31;22m[124416533][0498][0x7ef28781a0][VPSS_MPP][ERROR] mpi_vpss.c: ar_hal_vpss_create : 870 bind_register_sender failed 0x80028003^[[0m
Jan 25 00:04:36 runsvdir: ^[[33;22m[124416533][0300][0x7f8081b200][VO_CORE][WARN] display_api.c: dc_init : 2258 use dc_reg_irq_by_cmd!^[[0m
Jan 25 00:04:36 runsvdir: ^[[31;22m[124416533][0300][0x7f8081b200][VO_CORE][ERROR] display_top.c: ar_display_dev_enable : 3521 not support set irq_type:^[[33;22m[124416534][0300][0x7f813ae200][AXIMGR_CORE][WARN] ar_aximgr_lb.c: ar_aximgr_lb_enable : 285
Jan 25 00:04:36 runsvdir: ^[[33;22m[124416534][0300][0x7f8081b200][AXIMGR_CORE][WARN] ar_aximgr_lb.c: ar_aximgr_lb_enable : 285 [LB] 1920 1024 1024^[[0m
Jan 25 00:04:36 runsvdir: ^[[35;22m[124416534][0300][0x7f81267200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:04:36 runsvdir: ^[[35;22m[124416534][0300][0x7f81267200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:04:36 runsvdir: ^[[35;22m[124416534][0300][0x7f813ae200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:04:36 runsvdir: ^[[35;22m[124416535][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:04:36 runsvdir: ^[[35;22m[124416535][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:04:36 runsvdir: ^[[35;22m[124416535][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:04:36 runsvdir: 36e7d000 372b7000 373c7000 0 0 0 374d1000 3790b000 37a1b000 0 0 0 
Jan 25 00:04:36 runsvdir: aec index 198685398 line 1125 gain 1.000000 
Jan 25 00:04:36 runsvdir: ^[[35;22m[124416535][0300][0x7f813ae200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:04:36 runsvdir: ^[[35;22m[124416535][0300][0x7f813ae200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:04:36 runsvdir: 374d7000 37911000 37a21000 0 0 0 37b2b000 37f65000 38075000 0 0 0 
Jan 25 00:04:36 runsvdir: ^[[35;22m[124416537][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:04:36 runsvdir: ^[[35;22m[124416539][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:04:36 runsvdir: ^[[33;22m[124417829][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:04:36 runsvdir: ^[[0m
Jan 25 00:04:36 runsvdir: ^[[33;22m[124417856][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:04:36 runsvdir: ^[[0m
Jan 25 00:04:36 runsvdir: ^[[33;22m[124417869][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:04:36 runsvdir: ^[[0m
Jan 25 00:04:37 runsvdir: ^[[33;22m[124417936][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s iALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:37 runsvdir: [audio service]:try_again
Jan 25 00:04:37 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:37 runsvdir: [audio service]:try_again
Jan 25 00:04:37 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:37 runsvdir: [audio service]:try_again
Jan 25 00:04:37 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:37 runsvdir: [audio service]:try_again
Jan 25 00:04:37 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:37 runsvdir: [audio service]:try_again
Jan 25 00:04:39 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:39 runsvdir: [audio service]:try_again
Jan 25 00:04:40 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:40 runsvdir: [audio service]:try_again
Jan 25 00:04:40 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:40 runsvdir: [audio service]:try_again
Jan 25 00:04:40 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:40 runsvdir: [audio service]:try_again
Jan 25 00:04:41 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:41 runsvdir: [audio service]:try_again
Jan 25 00:04:41 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:41 runsvdir: [audio service]:try_again
Jan 25 00:04:41 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:41 runsvdir: [audio service]:try_again
Jan 25 00:04:41 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:41 runsvdir: [audio service]:try_again
Jan 25 00:04:41 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:41 runsvdir: [audio service]:try_again
Jan 25 00:04:41 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:41 runsvdir: [audio service]:try_again
Jan 25 00:04:41 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:41 runsvdir: [audio service]:try_again
Jan 25 00:04:41 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:41 runsvdir: [audio service]:try_again
Jan 25 00:04:41 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:41 runsvdir: [audio service]:try_again
Jan 25 00:04:41 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:41 runsvdir: [audio service]:try_again
Jan 25 00:04:42 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:42 runsvdir: [audio service]:try_again
Jan 25 00:04:44 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:44 runsvdir: [audio service]:try_again
Jan 25 00:04:44 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:44 runsvdir: [audio service]:try_again
Jan 25 00:04:44 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:44 runsvdir: [audio service]:try_again
Jan 25 00:04:46 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:46 runsvdir: [audio service]:try_again
Jan 25 00:04:47 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:47 runsvdir: [audio service]:try_again
Jan 25 00:04:47 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:47 runsvdir: [audio service]:try_again
Jan 25 00:04:47 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:47 runsvdir: [audio service]:try_again
Jan 25 00:04:47 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:47 runsvdir: [audio service]:try_again
Jan 25 00:04:49 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:49 runsvdir: [audio service]:try_again
Jan 25 00:04:50 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:50 runsvdir: [audio service]:try_again
Jan 25 00:04:50 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:50 runsvdir: [audio service]:try_again
Jan 25 00:04:50 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:50 runsvdir: [audio service]:try_again
Jan 25 00:04:50 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:50 runsvdir: [audio service]:try_again
Jan 25 00:04:53 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:53 runsvdir: [audio service]:try_again
Jan 25 00:04:53 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:53 runsvdir: [audio service]:try_again
Jan 25 00:04:57 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:57 runsvdir: [audio service]:try_again
Jan 25 00:04:59 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:59 runsvdir: [audio service]:try_again
Jan 25 00:04:59 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:59 runsvdir: [audio service]:try_again
Jan 25 00:04:59 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:59 runsvdir: [audio service]:try_again
Jan 25 00:04:59 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:04:59 runsvdir: [audio service]:try_again
Jan 25 00:05:00 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:00 runsvdir: [audio service]:try_again
Jan 25 00:05:00 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:00 runsvdir: [audio service]:try_again
Jan 25 00:05:00 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:00 runsvdir: [audio service]:try_again
Jan 25 00:05:00 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:00 runsvdir: [audio service]:try_again
Jan 25 00:05:00 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:00 runsvdir: [audio service]:try_again
Jan 25 00:05:00 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:00 runsvdir: [audio service]:try_again
Jan 25 00:05:00 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:00 runsvdir: [audio service]:try_again
Jan 25 00:05:00 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:00 runsvdir: [audio service]:try_again
Jan 25 00:05:01 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:01 runsvdir: [audio service]:try_again
Jan 25 00:05:01 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:01 runsvdir: [audio service]:try_again
Jan 25 00:05:01 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:01 runsvdir: [audio service]:try_again
Jan 25 00:05:01 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:01 runsvdir: [audio service]:try_again
Jan 25 00:05:02 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:02 runsvdir: [audio service]:try_again
Jan 25 00:05:02 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:02 runsvdir: [audio service]:try_again
Jan 25 00:05:05 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:05 runsvdir: [audio service]:try_again
Jan 25 00:05:08 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:08 runsvdir: [audio service]:try_again
Jan 25 00:05:08 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:08 runsvdir: [audio service]:try_again
Jan 25 00:05:08 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:08 runsvdir: [audio service]:try_again
Jan 25 00:05:09 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:09 runsvdir: [audio service]:try_again
Jan 25 00:05:09 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:09 runsvdir: [audio service]:try_again
Jan 25 00:05:09 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:09 runsvdir: [audio service]:try_again
Jan 25 00:05:09 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:09 runsvdir: [audio service]:try_again
Jan 25 00:05:09 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:09 runsvdir: [audio service]:try_again
Jan 25 00:05:09 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:09 runsvdir: [audio service]:try_again
Jan 25 00:05:12 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:12 runsvdir: [audio service]:try_again
Jan 25 00:05:13 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:13 runsvdir: [audio service]:try_again
Jan 25 00:05:14 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:14 runsvdir: [audio service]:try_again
Jan 25 00:05:14 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:14 runsvdir: [audio service]:try_again
Jan 25 00:05:14 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:14 runsvdir: [audio service]:try_again
Jan 25 00:05:14 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:14 runsvdir: [audio service]:try_again
Jan 25 00:05:14 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:14 runsvdir: [audio service]:try_again
Jan 25 00:05:14 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:14 runsvdir: [audio service]:try_again
Jan 25 00:05:14 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:14 runsvdir: [audio service]:try_again
Jan 25 00:05:15 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:15 runsvdir: [audio service]:try_again
Jan 25 00:05:15 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:15 runsvdir: [audio service]:try_again
Jan 25 00:05:17 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:17 runsvdir: [audio service]:try_again
Jan 25 00:05:18 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:18 runsvdir: [audio service]:try_again
Jan 25 00:05:19 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:19 runsvdir: [audio service]:try_again
Jan 25 00:05:21 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:21 runsvdir: [audio service]:try_again
Jan 25 00:05:21 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:21 runsvdir: [audio service]:try_again
Jan 25 00:05:22 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:22 runsvdir: [audio service]:try_again
Jan 25 00:05:22 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:22 runsvdir: [audio service]:try_again
Jan 25 00:05:23 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:23 runsvdir: [audio service]:try_again
Jan 25 00:05:23 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:23 runsvdir: [audio service]:try_again
Jan 25 00:05:23 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:23 runsvdir: [audio service]:try_again
Jan 25 00:05:24 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:24 runsvdir: [audio service]:try_again
Jan 25 00:05:24 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:24 runsvdir: [audio service]:try_again
Jan 25 00:05:24 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:24 runsvdir: [audio service]:try_again
Jan 25 00:05:24 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:24 runsvdir: [audio service]:try_again
Jan 25 00:05:24 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:24 runsvdir: [audio service]:try_again
Jan 25 00:05:24 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:24 runsvdir: [audio service]:try_again
Jan 25 00:05:26 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:26 runsvdir: [audio service]:try_again
Jan 25 00:05:26 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:26 runsvdir: [audio service]:try_again
Jan 25 00:05:26 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:26 runsvdir: [audio service]:try_again
Jan 25 00:05:26 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:26 runsvdir: [audio service]:try_again
Jan 25 00:05:26 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:26 runsvdir: [audio service]:try_again
Jan 25 00:05:26 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:26 runsvdir: [audio service]:try_again
Jan 25 00:05:26 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:26 runsvdir: [audio service]:try_again
Jan 25 00:05:26 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:26 runsvdir: [audio service]:try_again
Jan 25 00:05:27 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:27 runsvdir: [audio service]:try_again
Jan 25 00:05:27 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:27 runsvdir: [audio service]:try_again
Jan 25 00:05:27 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:27 runsvdir: [audio service]:try_again
Jan 25 00:05:27 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:27 runsvdir: [audio service]:try_again
Jan 25 00:05:29 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:29 runsvdir: [audio service]:try_again
Jan 25 00:05:30 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:30 runsvdir: [audio service]:try_again
Jan 25 00:05:30 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:30 runsvdir: [audio service]:try_again
Jan 25 00:05:30 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:30 runsvdir: [audio service]:try_again
Jan 25 00:05:30 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:30 runsvdir: [audio service]:try_again
Jan 25 00:05:30 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:30 runsvdir: [audio service]:try_again
Jan 25 00:05:30 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:30 runsvdir: [audio service]:try_again
Jan 25 00:05:30 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:30 runsvdir: [audio service]:try_again
Jan 25 00:05:43 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:43 runsvdir: [audio service]:try_again
Jan 25 00:05:43 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:43 runsvdir: [audio service]:try_again
Jan 25 00:05:47 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:47 runsvdir: [audio service]:try_again
Jan 25 00:05:48 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:48 runsvdir: [audio service]:try_again
Jan 25 00:05:49 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:49 runsvdir: [audio service]:try_again
Jan 25 00:05:49 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:49 runsvdir: [audio service]:try_again
Jan 25 00:05:49 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:49 runsvdir: [audio service]:try_again
Jan 25 00:05:49 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:49 runsvdir: [audio service]:try_again
Jan 25 00:05:49 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:49 runsvdir: [audio service]:try_again
Jan 25 00:05:51 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:51 runsvdir: [audio service]:try_again
Jan 25 00:05:59 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:05:59 runsvdir: [audio service]:try_again
Jan 25 00:06:00 runsvdir: nnel output
Jan 25 00:06:00 runsvdir: DisplayService I : check oled status:close, ret = 0
Jan 25 00:06:00 runsvdir: DisplayService I : set duty 0
Jan 25 00:06:00 runsvdir: DisplayService I : Setting new LUT for mode: dp_scaler_lut_direct_mode_1
Jan 25 00:06:00 runsvdir: DisplayService I : <timestamp init isp done> -- [ 262.815 ]
Jan 25 00:06:00 runsvdir: DisplayService I : current dpisp status update :2
Jan 25 00:06:00 runsvdir: DisplayService I : <timestamp open/close oled> -- [ 262.831 ]
Jan 25 00:06:00 runsvdir: DisplayService I : Screen successfully opened.
Jan 25 00:06:00 runsvdir: DisplayService I : set duty 30
Jan 25 00:06:00 runsvdir: DisplayService I : check oled status:open, ret = 0
Jan 25 00:06:00 runsvdir: DisplayService I : set duty 30
Jan 25 00:06:00 runsvdir: DisplayService I : get dp info 3840x1080@60
Jan 25 00:06:00 runsvdir: DisplayService W : can't find node 0x7f8c002eb0, ignore this release
Jan 25 00:06:00 runsvdir: DisplayService W : can't find node 0x7f8c003180, ignore this release
Jan 25 00:06:00 runsvdir: DisplayService W : can't find node 0x7f8c002370, ignore this release
Jan 25 00:06:00 runsvdir: DisplayService I : Get frame stats: Successes 52.00, Failures 0.00
Jan 25 00:06:00 runsvdir: DisplayService I : dpvsync : 57.67
Jan 25 00:06:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:06:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:06:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:06:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:06:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:06:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:06:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:06:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:06:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:06:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:06:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:06:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:06:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:06:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:06:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:06:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:06:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:06:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:06:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:06:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:06:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:06:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:06:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:06:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:06:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:06:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:06:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:06:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:06:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:06:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:06:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:06:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:06:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:06:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:06:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:06:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:06:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:06:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:06:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:06:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:06:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:06:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:06:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:06:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:06:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:06:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:06:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:06:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:06:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:06:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:06:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:06:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:06:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:06:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:06:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:06:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:06:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:06:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:06:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:06:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:06:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:06:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:06:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:06:01 runsvdir: DisplayServiceALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:06:01 runsvdir: [audio service]:try_again
Jan 25 00:06:06 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:06:06 runsvdir: [audio service]:try_again
Jan 25 00:06:06 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:06:06 runsvdir: [audio service]:try_again
Jan 25 00:06:08 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:06:08 runsvdir: [audio service]:try_again
Jan 25 00:06:11 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:06:11 runsvdir: [audio service]:try_again
Jan 25 00:06:11 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:06:11 runsvdir: [audio service]:try_again
Jan 25 00:06:19 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:06:19 runsvdir: [audio service]:try_again
Jan 25 00:06:22 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:06:22 runsvdir: [audio service]:try_again
Jan 25 00:06:24 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:06:24 runsvdir: [audio service]:try_again
Jan 25 00:06:32 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:06:32 runsvdir: [audio service]:try_again
Jan 25 00:06:33 runsvdir: n int lost 2 read num 2
Jan 25 00:06:33 runsvdir: ^[[0m
Jan 25 00:06:33 runsvdir: ^[[33;22m[124417949][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:33 runsvdir: ^[[0m
Jan 25 00:06:33 runsvdir: ^[[33;22m[124418109][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:33 runsvdir: ^[[0m
Jan 25 00:06:33 runsvdir: ^[[33;22m[124418600][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:33 runsvdir: ^[[0m
Jan 25 00:06:33 runsvdir: ^[[33;22m[124418640][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:33 runsvdir: ^[[0m
Jan 25 00:06:33 runsvdir: ^[[33;22m[124419603][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:33 runsvdir: ^[[0m
Jan 25 00:06:33 runsvdir: ^[[33;22m[124419920][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:33 runsvdir: ^[[0m
Jan 25 00:06:33 runsvdir: ^[[33;22m[124420147][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:33 runsvdir: ^[[0m
Jan 25 00:06:33 runsvdir: ^[[33;22m[124420213][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:33 runsvdir: ^[[0m
Jan 25 00:06:33 runsvdir: ^[[33;22m[124420677][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:33 runsvdir: ^[[0m
Jan 25 00:06:33 runsvdir: ^[[33;22m[124421007][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:33 runsvdir: ^[[0m
Jan 25 00:06:33 runsvdir: ^[[33;22m[124421377][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:33 runsvdir: ^[[0m
Jan 25 00:06:33 runsvdir: ^[[33;22m[124422291][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:33 runsvdir: ^[[0m
Jan 25 00:06:33 runsvdir: ^[[33;22m[124422384][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:33 runsvdir: ^[[0m
Jan 25 00:06:33 runsvdir: ^[[33;22m[124422394][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:33 runsvdir: ^[[0m
Jan 25 00:06:33 runsvdir: ^[[33;22m[124423598][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:33 runsvdir: ^[[0m
Jan 25 00:06:33 runsvdir: ^[[33;22m[124424551][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:33 runsvdir: ^[[0m
Jan 25 00:06:33 runsvdir: ^[[33;22m[124424778][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:33 runsvdir: ^[[0m
Jan 25 00:06:33 runsvdir: ^[[33;22m[124425028][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:33 runsvdir: ^[[0m
Jan 25 00:06:33 runsvdir: ^[[33;22m[124426022][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:33 runsvdir: ^[[0m
Jan 25 00:06:33 runsvdir: ^[[33;22m[124426642][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:33 runsvdir: ^[[0m
Jan 25 00:06:33 runsvdir: ^[[33;22m[124426645][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:33 runsvdir: ^[[0m
Jan 25 00:06:33 runsvdir: ^[[33;22m[124426659][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:33 runsvdir: ^[[0m
Jan 25 00:06:33 runsvdir: ^[[33;22m[124426672][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:33 runsvdir: ^[[0m
Jan 25 00:06:33 runsvdir: ^[[33;22m[124426695][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:33 runsvdir: ^[[0m
Jan 25 00:06:33 runsvdir: ^[[33;22m[124426789][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:33 runsvdir: ^[[0m
Jan 25 00:06:33 runsvdir: ^[[33;22m[124426802][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:33 runsvdir: ^[[0m
Jan 25 00:06:33 runsvdir: ^[[33;22m[124426829][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:33 runsvdir: ^[[0m
Jan 25 00:06:33 runsvdir: ^[[33;22m[124427319][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:33 runsvdir: ^[[0m
Jan 25 00:06:33 runsvdir: ^[[33;22m[124427622][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:33 runsvdir: ^[[0m
Jan 25 00:06:33 runsvdir: ^[[33;22m[124427662][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:33 runsvdir: ^[[0m
Jan 25 00:06:33 runsvdir: ^[[33;22m[124427782][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:33 runsvdir: ^[[0m
Jan 25 00:06:33 runsvdir: ^[[33;22m[124428443][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:06:33 runsvdir: ^[[0m
Jan 25 00:06:33 runsvdir: ^[[33;22m[124429650][0300][0x7f823e5ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:06:33 runsvdir: [audio service]:try_again
Jan 25 00:06:35 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:06:35 runsvdir: [audio service]:try_again
Jan 25 00:06:35 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:06:35 runsvdir: [audio service]:try_again
Jan 25 00:06:36 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:06:36 runsvdir: [audio service]:try_again
Jan 25 00:06:37 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:06:37 runsvdir: [audio service]:try_again
Jan 25 00:06:37 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:06:37 runsvdir: [audio service]:try_again
Jan 25 00:06:39 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:06:39 runsvdir: [audio service]:try_again
Jan 25 00:06:39 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:06:39 runsvdir: [audio service]:try_again
Jan 25 00:06:39 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:06:39 runsvdir: [audio service]:try_again
Jan 25 00:06:43 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:06:43 runsvdir: [audio service]:try_again
Jan 25 00:06:50 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:06:50 runsvdir: [audio service]:try_again
Jan 25 00:06:50 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:06:50 runsvdir: [audio service]:try_again
Jan 25 00:06:50 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:06:50 runsvdir: [audio service]:try_again
Jan 25 00:06:50 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:06:50 runsvdir: [audio service]:try_again
Jan 25 00:06:52 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:06:52 runsvdir: [audio service]:try_again
Jan 25 00:06:52 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:06:53 runsvdir: [audio service]:try_again
Jan 25 00:06:53 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:06:53 runsvdir: [audio service]:try_again
Jan 25 00:06:57 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:06:57 runsvdir: [audio service]:try_again
Jan 25 00:06:57 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:06:57 runsvdir: [audio service]:try_again
Jan 25 00:06:57 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:06:57 runsvdir: [audio service]:try_again
Jan 25 00:06:59 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:06:59 runsvdir: [audio service]:try_again
Jan 25 00:06:59 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:06:59 runsvdir: [audio service]:try_again
Jan 25 00:06:59 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:06:59 runsvdir: [audio service]:try_again
Jan 25 00:06:59 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:06:59 runsvdir: [audio service]:try_again
Jan 25 00:06:59 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:06:59 runsvdir: [audio service]:try_again
Jan 25 00:07:01 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:01 runsvdir: [audio service]:try_again
Jan 25 00:07:04 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:04 runsvdir: [audio service]:try_again
Jan 25 00:07:04 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:04 runsvdir: [audio service]:try_again
Jan 25 00:07:04 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:04 runsvdir: [audio service]:try_again
Jan 25 00:07:04 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:04 runsvdir: [audio service]:try_again
Jan 25 00:07:04 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:04 runsvdir: [audio service]:try_again
Jan 25 00:07:05 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:05 runsvdir: [audio service]:try_again
Jan 25 00:07:05 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:05 runsvdir: [audio service]:try_again
Jan 25 00:07:06 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:06 runsvdir: [audio service]:try_again
Jan 25 00:07:09 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:09 runsvdir: [audio service]:try_again
Jan 25 00:07:09 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:09 runsvdir: [audio service]:try_again
Jan 25 00:07:09 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:09 runsvdir: [audio service]:try_again
Jan 25 00:07:10 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:10 runsvdir: [audio service]:try_again
Jan 25 00:07:10 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:10 runsvdir: [audio service]:try_again
Jan 25 00:07:10 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:10 runsvdir: [audio service]:try_again
Jan 25 00:07:13 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:13 runsvdir: [audio service]:try_again
Jan 25 00:07:13 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:13 runsvdir: [audio service]:try_again
Jan 25 00:07:13 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:13 runsvdir: [audio service]:try_again
Jan 25 00:07:13 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:13 runsvdir: [audio service]:try_again
Jan 25 00:07:14 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:14 runsvdir: [audio service]:try_again
Jan 25 00:07:15 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:15 runsvdir: [audio service]:try_again
Jan 25 00:07:15 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:15 runsvdir: [audio service]:try_again
Jan 25 00:07:15 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:15 runsvdir: [audio service]:try_again
Jan 25 00:07:15 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:15 runsvdir: [audio service]:try_again
Jan 25 00:07:15 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:15 runsvdir: [audio service]:try_again
Jan 25 00:07:15 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:15 runsvdir: [audio service]:try_again
Jan 25 00:07:15 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:15 runsvdir: [audio service]:try_again
Jan 25 00:07:18 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:18 runsvdir: [audio service]:try_again
Jan 25 00:07:18 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:18 runsvdir: [audio service]:try_again
Jan 25 00:07:20 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:20 runsvdir: [audio service]:try_again
Jan 25 00:07:21 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:21 runsvdir: [audio service]:try_again
Jan 25 00:07:21 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:21 runsvdir: [audio service]:try_again
Jan 25 00:07:21 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:21 runsvdir: [audio service]:try_again
Jan 25 00:07:21 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:21 runsvdir: [audio service]:try_again
Jan 25 00:07:21 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:21 runsvdir: [audio service]:try_again
Jan 25 00:07:21 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:21 runsvdir: [audio service]:try_again
Jan 25 00:07:21 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:21 runsvdir: [audio service]:try_again
Jan 25 00:07:21 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:21 runsvdir: [audio service]:try_again
Jan 25 00:07:22 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:22 runsvdir: [audio service]:try_again
Jan 25 00:07:23 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:23 runsvdir: [audio service]:try_again
Jan 25 00:07:24 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:24 runsvdir: [audio service]:try_again
Jan 25 00:07:24 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:24 runsvdir: [audio service]:try_again
Jan 25 00:07:24 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:24 runsvdir: [audio service]:try_again
Jan 25 00:07:24 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:24 runsvdir: [audio service]:try_again
Jan 25 00:07:24 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:24 runsvdir: [audio service]:try_again
Jan 25 00:07:24 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:24 runsvdir: [audio service]:try_again
Jan 25 00:07:25 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:25 runsvdir: [audio service]:try_again
Jan 25 00:07:25 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:25 runsvdir: [audio service]:try_again
Jan 25 00:07:26 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:26 runsvdir: [audio service]:try_again
Jan 25 00:07:26 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:26 runsvdir: [audio service]:try_again
Jan 25 00:07:26 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:26 runsvdir: [audio service]:try_again
Jan 25 00:07:26 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:26 runsvdir: [audio service]:try_again
Jan 25 00:07:28 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:28 runsvdir: [audio service]:try_again
Jan 25 00:07:29 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:29 runsvdir: [audio service]:try_again
Jan 25 00:07:29 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:29 runsvdir: [audio service]:try_again
Jan 25 00:07:29 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:29 runsvdir: [audio service]:try_again
Jan 25 00:07:29 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:29 runsvdir: [audio service]:try_again
Jan 25 00:07:29 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:29 runsvdir: [audio service]:try_again
Jan 25 00:07:31 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:31 runsvdir: [audio service]:try_again
Jan 25 00:07:34 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:34 runsvdir: [audio service]:try_again
Jan 25 00:07:34 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:34 runsvdir: [audio service]:try_again
Jan 25 00:07:34 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:34 runsvdir: [audio service]:try_again
Jan 25 00:07:35 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:35 runsvdir: [audio service]:try_again
Jan 25 00:07:35 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:35 runsvdir: [audio service]:try_again
Jan 25 00:07:35 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:35 runsvdir: [audio service]:try_again
Jan 25 00:07:35 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:35 runsvdir: [audio service]:try_again
Jan 25 00:07:35 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:35 runsvdir: [audio service]:try_again
Jan 25 00:07:36 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:36 runsvdir: [audio service]:try_again
Jan 25 00:07:38 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:38 runsvdir: [audio service]:try_again
Jan 25 00:07:38 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:38 runsvdir: [audio service]:try_again
Jan 25 00:07:38 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:38 runsvdir: [audio service]:try_again
Jan 25 00:07:39 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:39 runsvdir: [audio service]:try_again
Jan 25 00:07:39 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:39 runsvdir: [audio service]:try_again
Jan 25 00:07:39 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:39 runsvdir: [audio service]:try_again
Jan 25 00:07:41 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:41 runsvdir: [audio service]:try_again
Jan 25 00:07:41 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:41 runsvdir: [audio service]:try_again
Jan 25 00:07:41 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:41 runsvdir: [audio service]:try_again
Jan 25 00:07:41 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:41 runsvdir: [audio service]:try_again
Jan 25 00:07:41 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:41 runsvdir: [audio service]:try_again
Jan 25 00:07:41 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:41 runsvdir: [audio service]:try_again
Jan 25 00:07:41 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:41 runsvdir: [audio service]:try_again
Jan 25 00:07:45 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:45 runsvdir: [audio service]:try_again
Jan 25 00:07:45 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:45 runsvdir: [audio service]:try_again
Jan 25 00:07:45 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:45 runsvdir: [audio service]:try_again
Jan 25 00:07:45 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:45 runsvdir: [audio service]:try_again
Jan 25 00:07:45 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:45 runsvdir: [audio service]:try_again
Jan 25 00:07:45 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:45 runsvdir: [audio service]:try_again
Jan 25 00:07:45 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:45 runsvdir: [audio service]:try_again
Jan 25 00:07:45 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:45 runsvdir: [audio service]:try_again
Jan 25 00:07:45 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:45 runsvdir: [audio service]:try_again
Jan 25 00:07:46 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:46 runsvdir: [audio service]:try_again
Jan 25 00:07:46 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:46 runsvdir: [audio service]:try_again
Jan 25 00:07:46 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:46 runsvdir: [audio service]:try_again
Jan 25 00:07:46 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:46 runsvdir: [audio service]:try_again
Jan 25 00:07:46 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:46 runsvdir: [audio service]:try_again
Jan 25 00:07:46 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:46 runsvdir: [audio service]:try_again
Jan 25 00:07:46 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:46 runsvdir: [audio service]:try_again
Jan 25 00:07:49 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:49 runsvdir: [audio service]:try_again
Jan 25 00:07:50 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:50 runsvdir: [audio service]:try_again
Jan 25 00:07:51 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:51 runsvdir: [audio service]:try_again
Jan 25 00:07:51 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:51 runsvdir: [audio service]:try_again
Jan 25 00:07:51 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:51 runsvdir: [audio service]:try_again
Jan 25 00:07:51 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:51 runsvdir: [audio service]:try_again
Jan 25 00:07:54 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:54 runsvdir: [audio service]:try_again
Jan 25 00:07:54 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:54 runsvdir: [audio service]:try_again
Jan 25 00:07:54 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:54 runsvdir: [audio service]:try_again
Jan 25 00:07:54 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:54 runsvdir: [audio service]:try_again
Jan 25 00:07:54 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:54 runsvdir: [audio service]:try_again
Jan 25 00:07:55 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:55 runsvdir: [audio service]:try_again
Jan 25 00:07:56 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:56 runsvdir: [audio service]:try_again
Jan 25 00:07:57 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:57 runsvdir: [audio service]:try_again
Jan 25 00:07:57 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:57 runsvdir: [audio service]:try_again
Jan 25 00:07:57 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:57 runsvdir: [audio service]:try_again
Jan 25 00:07:59 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:07:59 runsvdir: [audio service]:try_again
Jan 25 00:08:00 runsvdir:  I : dpvsync : 60.00
Jan 25 00:08:00 runsvdir: DisplayService I : Get frame stats: Successes 60.33, Failures 0.00
Jan 25 00:08:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:08:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:08:00 runsvdir: DisplayService I : dpvsync : 60.33
Jan 25 00:08:00 runsvdir: DisplayService I : Get frame stats: Successes 59.67, Failures 0.00
Jan 25 00:08:00 runsvdir: DisplayService I : dpvsync : 59.67
Jan 25 00:08:00 runsvdir: DisplayService I : Get frame stats: Successes 60.33, Failures 0.00
Jan 25 00:08:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:08:00 runsvdir: DisplayService I : Get frame stats: Successes 59.67, Failures 0.00
Jan 25 00:08:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:08:00 runsvdir: DisplayService I : Get frame stats: Successes 60.33, Failures 0.00
Jan 25 00:08:00 runsvdir: DisplayService I : dpvsync : 60.33
Jan 25 00:08:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:08:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:08:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:08:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:08:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:08:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:08:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:08:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:08:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:08:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:08:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:08:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:08:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:08:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:08:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:08:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:08:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:08:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:08:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:08:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:08:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:08:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:08:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:08:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:08:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:08:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:08:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:08:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:08:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:08:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:08:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:08:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:08:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:08:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:08:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:08:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:08:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:08:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:08:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:08:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:08:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:08:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:08:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:08:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:08:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:08:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:08:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:08:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:08:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:08:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:08:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:08:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:08:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:08:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:08:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:08:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:08:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:08:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:08:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:08:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:08:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:08:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:08:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:08:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:08:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:08:00 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:08:00 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:08:01 runsvdir: DisplayService I : dpvsync : 6ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:01 runsvdir: [audio service]:try_again
Jan 25 00:08:01 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:01 runsvdir: [audio service]:try_again
Jan 25 00:08:02 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:02 runsvdir: [audio service]:try_again
Jan 25 00:08:02 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:02 runsvdir: [audio service]:try_again
Jan 25 00:08:03 runsvdir: 200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:03 runsvdir: ^[[0m
Jan 25 00:08:03 runsvdir: ^[[33;22m[124430100][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:03 runsvdir: ^[[0m
Jan 25 00:08:03 runsvdir: ^[[33;22m[124430347][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:03 runsvdir: ^[[0m
Jan 25 00:08:03 runsvdir: ^[[33;22m[124431647][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:03 runsvdir: ^[[0m
Jan 25 00:08:03 runsvdir: ^[[33;22m[124431687][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:03 runsvdir: ^[[0m
Jan 25 00:08:03 runsvdir: ^[[33;22m[124431700][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:03 runsvdir: ^[[0m
Jan 25 00:08:03 runsvdir: ^[[33;22m[124431780][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:03 runsvdir: ^[[0m
Jan 25 00:08:03 runsvdir: ^[[33;22m[124431897][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:03 runsvdir: ^[[0m
Jan 25 00:08:03 runsvdir: ^[[33;22m[124433044][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:03 runsvdir: ^[[0m
Jan 25 00:08:03 runsvdir: ^[[33;22m[124433061][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:03 runsvdir: ^[[0m
Jan 25 00:08:03 runsvdir: ^[[33;22m[124433064][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:03 runsvdir: ^[[0m
Jan 25 00:08:03 runsvdir: ^[[33;22m[124433454][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:03 runsvdir: ^[[0m
Jan 25 00:08:03 runsvdir: ^[[33;22m[124433458][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:03 runsvdir: ^[[0m
Jan 25 00:08:03 runsvdir: ^[[33;22m[124433471][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:03 runsvdir: ^[[0m
Jan 25 00:08:03 runsvdir: ^[[33;22m[124433484][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:03 runsvdir: ^[[0m
Jan 25 00:08:03 runsvdir: ^[[33;22m[124433911][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:03 runsvdir: ^[[0m
Jan 25 00:08:03 runsvdir: ^[[33;22m[124434214][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:03 runsvdir: ^[[0m
Jan 25 00:08:03 runsvdir: ^[[33;22m[124434238][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:03 runsvdir: ^[[0m
Jan 25 00:08:03 runsvdir: ^[[33;22m[124434661][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:03 runsvdir: ^[[0m
Jan 25 00:08:03 runsvdir: ^[[33;22m[124434665][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:03 runsvdir: ^[[0m
Jan 25 00:08:03 runsvdir: ^[[33;22m[124434678][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:03 runsvdir: ^[[0m
Jan 25 00:08:03 runsvdir: ^[[33;22m[124435141][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:03 runsvdir: ^[[0m
Jan 25 00:08:03 runsvdir: ^[[33;22m[124435485][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:03 runsvdir: ^[[0m
Jan 25 00:08:03 runsvdir: ^[[33;22m[124435935][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:03 runsvdir: ^[[0m
Jan 25 00:08:03 runsvdir: ^[[33;22m[124436372][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:03 runsvdir: ^[[0m
Jan 25 00:08:03 runsvdir: ^[[33;22m[124437295][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:03 runsvdir: ^[[0m
Jan 25 00:08:03 runsvdir: ^[[33;22m[124437299][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:03 runsvdir: ^[[0m
Jan 25 00:08:03 runsvdir: ^[[33;22m[124437535][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:03 runsvdir: ^[[0m
Jan 25 00:08:03 runsvdir: ^[[33;22m[124437589][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:03 runsvdir: ^[[0m
Jan 25 00:08:03 runsvdir: ^[[33;22m[124438142][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:03 runsvdir: ^[[0m
Jan 25 00:08:03 runsvdir: ^[[33;22m[124438186][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:03 runsvdir: ^[[0m
Jan 25 00:08:03 runsvdir: ^[[33;22m[124438226][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:08:03 runsvdir: ^[[0m
Jan 25 00:08:03 runsvdir: ^[[33;22m[124438609][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:03 runsvdir: [audio service]:try_again
Jan 25 00:08:03 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:03 runsvdir: [audio service]:try_again
Jan 25 00:08:03 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:03 runsvdir: [audio service]:try_again
Jan 25 00:08:06 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:06 runsvdir: [audio service]:try_again
Jan 25 00:08:06 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:06 runsvdir: [audio service]:try_again
Jan 25 00:08:07 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:07 runsvdir: [audio service]:try_again
Jan 25 00:08:07 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:07 runsvdir: [audio service]:try_again
Jan 25 00:08:07 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:07 runsvdir: [audio service]:try_again
Jan 25 00:08:12 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:12 runsvdir: [audio service]:try_again
Jan 25 00:08:12 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:12 runsvdir: [audio service]:try_again
Jan 25 00:08:12 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:12 runsvdir: [audio service]:try_again
Jan 25 00:08:12 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:12 runsvdir: [audio service]:try_again
Jan 25 00:08:12 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:12 runsvdir: [audio service]:try_again
Jan 25 00:08:12 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:12 runsvdir: [audio service]:try_again
Jan 25 00:08:12 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:12 runsvdir: [audio service]:try_again
Jan 25 00:08:14 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:14 runsvdir: [audio service]:try_again
Jan 25 00:08:14 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:14 runsvdir: [audio service]:try_again
Jan 25 00:08:14 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:14 runsvdir: [audio service]:try_again
Jan 25 00:08:15 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:15 runsvdir: [audio service]:try_again
Jan 25 00:08:17 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:17 runsvdir: [audio service]:try_again
Jan 25 00:08:17 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:17 runsvdir: [audio service]:try_again
Jan 25 00:08:17 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:17 runsvdir: [audio service]:try_again
Jan 25 00:08:17 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:17 runsvdir: [audio service]:try_again
Jan 25 00:08:18 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:18 runsvdir: [audio service]:try_again
Jan 25 00:08:18 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:18 runsvdir: [audio service]:try_again
Jan 25 00:08:18 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:18 runsvdir: [audio service]:try_again
Jan 25 00:08:18 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:18 runsvdir: [audio service]:try_again
Jan 25 00:08:19 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:19 runsvdir: [audio service]:try_again
Jan 25 00:08:19 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:19 runsvdir: [audio service]:try_again
Jan 25 00:08:19 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:19 runsvdir: [audio service]:try_again
Jan 25 00:08:19 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:19 runsvdir: [audio service]:try_again
Jan 25 00:08:20 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:20 runsvdir: [audio service]:try_again
Jan 25 00:08:20 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:20 runsvdir: [audio service]:try_again
Jan 25 00:08:20 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:20 runsvdir: [audio service]:try_again
Jan 25 00:08:20 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:20 runsvdir: [audio service]:try_again
Jan 25 00:08:20 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:20 runsvdir: [audio service]:try_again
Jan 25 00:08:20 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:20 runsvdir: [audio service]:try_again
Jan 25 00:08:20 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:20 runsvdir: [audio service]:try_again
Jan 25 00:08:21 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:21 runsvdir: [audio service]:try_again
Jan 25 00:08:21 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:21 runsvdir: [audio service]:try_again
Jan 25 00:08:22 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:22 runsvdir: [audio service]:try_again
Jan 25 00:08:22 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:22 runsvdir: [audio service]:try_again
Jan 25 00:08:22 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:22 runsvdir: [audio service]:try_again
Jan 25 00:08:22 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:22 runsvdir: [audio service]:try_again
Jan 25 00:08:22 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:22 runsvdir: [audio service]:try_again
Jan 25 00:08:22 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:22 runsvdir: [audio service]:try_again
Jan 25 00:08:22 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:22 runsvdir: [audio service]:try_again
Jan 25 00:08:22 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:22 runsvdir: [audio service]:try_again
Jan 25 00:08:22 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:22 runsvdir: [audio service]:try_again
Jan 25 00:08:23 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:23 runsvdir: [audio service]:try_again
Jan 25 00:08:28 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:28 runsvdir: [audio service]:try_again
Jan 25 00:08:28 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:28 runsvdir: [audio service]:try_again
Jan 25 00:08:28 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:28 runsvdir: [audio service]:try_again
Jan 25 00:08:28 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:28 runsvdir: [audio service]:try_again
Jan 25 00:08:28 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:28 runsvdir: [audio service]:try_again
Jan 25 00:08:28 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:28 runsvdir: [audio service]:try_again
Jan 25 00:08:29 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:29 runsvdir: [audio service]:try_again
Jan 25 00:08:29 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:29 runsvdir: [audio service]:try_again
Jan 25 00:08:29 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:29 runsvdir: [audio service]:try_again
Jan 25 00:08:29 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:29 runsvdir: [audio service]:try_again
Jan 25 00:08:29 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:29 runsvdir: [audio service]:try_again
Jan 25 00:08:33 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:33 runsvdir: [audio service]:try_again
Jan 25 00:08:33 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:33 runsvdir: [audio service]:try_again
Jan 25 00:08:33 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:33 runsvdir: [audio service]:try_again
Jan 25 00:08:33 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:33 runsvdir: [audio service]:try_again
Jan 25 00:08:34 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:34 runsvdir: [audio service]:try_again
Jan 25 00:08:34 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:34 runsvdir: [audio service]:try_again
Jan 25 00:08:34 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:34 runsvdir: [audio service]:try_again
Jan 25 00:08:34 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:34 runsvdir: [audio service]:try_again
Jan 25 00:08:35 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:35 runsvdir: [audio service]:try_again
Jan 25 00:08:35 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:35 runsvdir: [audio service]:try_again
Jan 25 00:08:37 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:37 runsvdir: [audio service]:try_again
Jan 25 00:08:37 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:37 runsvdir: [audio service]:try_again
Jan 25 00:08:37 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:37 runsvdir: [audio service]:try_again
Jan 25 00:08:37 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:37 runsvdir: [audio service]:try_again
Jan 25 00:08:37 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:37 runsvdir: [audio service]:try_again
Jan 25 00:08:37 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:37 runsvdir: [audio service]:try_again
Jan 25 00:08:38 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:38 runsvdir: [audio service]:try_again
Jan 25 00:08:38 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:38 runsvdir: [audio service]:try_again
Jan 25 00:08:38 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:38 runsvdir: [audio service]:try_again
Jan 25 00:08:39 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:39 runsvdir: [audio service]:try_again
Jan 25 00:08:39 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:39 runsvdir: [audio service]:try_again
Jan 25 00:08:39 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:39 runsvdir: [audio service]:try_again
Jan 25 00:08:39 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:39 runsvdir: [audio service]:try_again
Jan 25 00:08:39 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:39 runsvdir: [audio service]:try_again
Jan 25 00:08:39 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:39 runsvdir: [audio service]:try_again
Jan 25 00:08:39 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:39 runsvdir: [audio service]:try_again
Jan 25 00:08:40 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:40 runsvdir: [audio service]:try_again
Jan 25 00:08:40 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:40 runsvdir: [audio service]:try_again
Jan 25 00:08:40 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:40 runsvdir: [audio service]:try_again
Jan 25 00:08:40 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:40 runsvdir: [audio service]:try_again
Jan 25 00:08:40 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:40 runsvdir: [audio service]:try_again
Jan 25 00:08:40 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:40 runsvdir: [audio service]:try_again
Jan 25 00:08:40 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:40 runsvdir: [audio service]:try_again
Jan 25 00:08:40 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:40 runsvdir: [audio service]:try_again
Jan 25 00:08:40 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:40 runsvdir: [audio service]:try_again
Jan 25 00:08:40 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:40 runsvdir: [audio service]:try_again
Jan 25 00:08:40 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:40 runsvdir: [audio service]:try_again
Jan 25 00:08:41 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:41 runsvdir: [audio service]:try_again
Jan 25 00:08:41 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:41 runsvdir: [audio service]:try_again
Jan 25 00:08:41 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:41 runsvdir: [audio service]:try_again
Jan 25 00:08:41 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:41 runsvdir: [audio service]:try_again
Jan 25 00:08:41 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:41 runsvdir: [audio service]:try_again
Jan 25 00:08:42 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:42 runsvdir: [audio service]:try_again
Jan 25 00:08:42 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:42 runsvdir: [audio service]:try_again
Jan 25 00:08:42 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:42 runsvdir: [audio service]:try_again
Jan 25 00:08:42 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:42 runsvdir: [audio service]:try_again
Jan 25 00:08:42 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:42 runsvdir: [audio service]:try_again
Jan 25 00:08:42 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:42 runsvdir: [audio service]:try_again
Jan 25 00:08:43 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:43 runsvdir: [audio service]:try_again
Jan 25 00:08:43 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:43 runsvdir: [audio service]:try_again
Jan 25 00:08:43 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:43 runsvdir: [audio service]:try_again
Jan 25 00:08:43 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:43 runsvdir: [audio service]:try_again
Jan 25 00:08:43 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:43 runsvdir: [audio service]:try_again
Jan 25 00:08:43 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:43 runsvdir: [audio service]:try_again
Jan 25 00:08:43 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:43 runsvdir: [audio service]:try_again
Jan 25 00:08:43 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:43 runsvdir: [audio service]:try_again
Jan 25 00:08:45 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:45 runsvdir: [audio service]:try_again
Jan 25 00:08:45 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:45 runsvdir: [audio service]:try_again
Jan 25 00:08:46 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:46 runsvdir: [audio service]:try_again
Jan 25 00:08:46 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:46 runsvdir: [audio service]:try_again
Jan 25 00:08:46 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:46 runsvdir: [audio service]:try_again
Jan 25 00:08:46 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:46 runsvdir: [audio service]:try_again
Jan 25 00:08:46 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:46 runsvdir: [audio service]:try_again
Jan 25 00:08:46 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:46 runsvdir: [audio service]:try_again
Jan 25 00:08:47 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:47 runsvdir: [audio service]:try_again
Jan 25 00:08:47 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:47 runsvdir: [audio service]:try_again
Jan 25 00:08:47 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:47 runsvdir: [audio service]:try_again
Jan 25 00:08:51 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:51 runsvdir: [audio service]:try_again
Jan 25 00:08:51 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:51 runsvdir: [audio service]:try_again
Jan 25 00:08:53 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:53 runsvdir: [audio service]:try_again
Jan 25 00:08:54 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:54 runsvdir: [audio service]:try_again
Jan 25 00:08:54 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:54 runsvdir: [audio service]:try_again
Jan 25 00:08:54 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:54 runsvdir: [audio service]:try_again
Jan 25 00:08:54 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:54 runsvdir: [audio service]:try_again
Jan 25 00:08:54 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:54 runsvdir: [audio service]:try_again
Jan 25 00:08:54 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:54 runsvdir: [audio service]:try_again
Jan 25 00:08:54 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:54 runsvdir: [audio service]:try_again
Jan 25 00:08:54 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:54 runsvdir: [audio service]:try_again
Jan 25 00:08:55 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:55 runsvdir: [audio service]:try_again
Jan 25 00:08:57 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:57 runsvdir: [audio service]:try_again
Jan 25 00:08:57 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:57 runsvdir: [audio service]:try_again
Jan 25 00:08:59 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:59 runsvdir: [audio service]:try_again
Jan 25 00:08:59 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:59 runsvdir: [audio service]:try_again
Jan 25 00:08:59 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:59 runsvdir: [audio service]:try_again
Jan 25 00:08:59 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:59 runsvdir: [audio service]:try_again
Jan 25 00:08:59 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:59 runsvdir: [audio service]:try_again
Jan 25 00:08:59 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:08:59 runsvdir: [audio service]:try_again
Jan 25 00:09:04 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:04 runsvdir: [audio service]:try_again
Jan 25 00:09:05 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:05 runsvdir: [audio service]:try_again
Jan 25 00:09:07 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:07 runsvdir: [audio service]:try_again
Jan 25 00:09:09 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:09 runsvdir: [audio service]:try_again
Jan 25 00:09:09 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:09 runsvdir: [audio service]:try_again
Jan 25 00:09:10 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:10 runsvdir: [audio service]:try_again
Jan 25 00:09:10 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:10 runsvdir: [audio service]:try_again
Jan 25 00:09:13 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:13 runsvdir: [audio service]:try_again
Jan 25 00:09:13 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:13 runsvdir: [audio service]:try_again
Jan 25 00:09:13 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:13 runsvdir: [audio service]:try_again
Jan 25 00:09:13 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:13 runsvdir: [audio service]:try_again
Jan 25 00:09:14 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:14 runsvdir: [audio service]:try_again
Jan 25 00:09:14 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:14 runsvdir: [audio service]:try_again
Jan 25 00:09:14 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:14 runsvdir: [audio service]:try_again
Jan 25 00:09:14 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:14 runsvdir: [audio service]:try_again
Jan 25 00:09:14 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:14 runsvdir: [audio service]:try_again
Jan 25 00:09:14 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:14 runsvdir: [audio service]:try_again
Jan 25 00:09:14 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:14 runsvdir: [audio service]:try_again
Jan 25 00:09:15 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:15 runsvdir: [audio service]:try_again
Jan 25 00:09:15 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:15 runsvdir: [audio service]:try_again
Jan 25 00:09:16 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:16 runsvdir: [audio service]:try_again
Jan 25 00:09:16 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:16 runsvdir: [audio service]:try_again
Jan 25 00:09:16 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:16 runsvdir: [audio service]:try_again
Jan 25 00:09:17 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:17 runsvdir: [audio service]:try_again
Jan 25 00:09:17 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:17 runsvdir: [audio service]:try_again
Jan 25 00:09:17 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:17 runsvdir: [audio service]:try_again
Jan 25 00:09:17 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:17 runsvdir: [audio service]:try_again
Jan 25 00:09:17 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:17 runsvdir: [audio service]:try_again
Jan 25 00:09:22 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:22 runsvdir: [audio service]:try_again
Jan 25 00:09:22 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:22 runsvdir: [audio service]:try_again
Jan 25 00:09:26 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:26 runsvdir: [audio service]:try_again
Jan 25 00:09:26 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:26 runsvdir: [audio service]:try_again
Jan 25 00:09:28 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:28 runsvdir: [audio service]:try_again
Jan 25 00:09:28 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:28 runsvdir: [audio service]:try_again
Jan 25 00:09:28 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:28 runsvdir: [audio service]:try_again
Jan 25 00:09:28 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:28 runsvdir: [audio service]:try_again
Jan 25 00:09:36 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:36 runsvdir: [audio service]:try_again
Jan 25 00:09:37 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:37 runsvdir: [audio service]:try_again
Jan 25 00:09:38 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:38 runsvdir: [audio service]:try_again
Jan 25 00:09:45 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:45 runsvdir: [audio service]:try_again
Jan 25 00:09:50 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:50 runsvdir: [audio service]:try_again
Jan 25 00:09:53 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:53 runsvdir: [audio service]:try_again
Jan 25 00:09:54 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:54 runsvdir: [audio service]:try_again
Jan 25 00:09:57 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:57 runsvdir: [audio service]:try_again
Jan 25 00:09:57 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:57 runsvdir: [audio service]:try_again
Jan 25 00:09:57 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:57 runsvdir: [audio service]:try_again
Jan 25 00:09:58 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:58 runsvdir: [audio service]:try_again
Jan 25 00:09:58 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:58 runsvdir: [audio service]:try_again
Jan 25 00:09:58 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:09:58 runsvdir: [audio service]:try_again
Jan 25 00:10:04 runsvdir: 0.00
Jan 25 00:10:04 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:04 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:04 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:04 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:04 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:04 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:04 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:04 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:04 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:04 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:04 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:04 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:04 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:04 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:04 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:04 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:04 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:04 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:04 runsvdir: DisplayService I : Get frame stats: Successes 60.33, Failures 0.00
Jan 25 00:10:04 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:04 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:04 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:04 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:04 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:04 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:04 runsvdir: DisplayService I : dpvsync : 60.33
Jan 25 00:10:04 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:04 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:04 runsvdir: DisplayService I : Get frame stats: Successes 59.67, Failures 0.00
Jan 25 00:10:04 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:04 runsvdir: DisplayService I : Get frame stats: Successes 60.33, Failures 0.00
Jan 25 00:10:04 runsvdir: DisplayService I : dpvsync : 59.67
Jan 25 00:10:04 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:04 runsvdir: DisplayService I : dpvsync : 60.33
Jan 25 00:10:04 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:04 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:04 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:04 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:04 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:04 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:04 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:04 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:04 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:04 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:04 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:04 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:04 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:04 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:04 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:04 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:04 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:04 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:04 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:04 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:04 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:04 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:04 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:04 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:04 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:04 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:04 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:04 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:04 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:04 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:04 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:04 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:04 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:04 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:04 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:04 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:04 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:04 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:04 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:04 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:04 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:04 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:04 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:04 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:04 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:04 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:05 runsvdir: DisplayServALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:05 runsvdir: [audio service]:try_again
Jan 25 00:10:09 runsvdir: int lost 2 read num 2
Jan 25 00:10:09 runsvdir: ^[[0m
Jan 25 00:10:09 runsvdir: ^[[33;22m[124438742][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:09 runsvdir: ^[[0m
Jan 25 00:10:09 runsvdir: ^[[33;22m[124439523][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:09 runsvdir: ^[[0m
Jan 25 00:10:09 runsvdir: ^[[33;22m[124440633][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:09 runsvdir: ^[[0m
Jan 25 00:10:09 runsvdir: ^[[33;22m[124440860][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:09 runsvdir: ^[[0m
Jan 25 00:10:09 runsvdir: ^[[33;22m[124440873][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:09 runsvdir: ^[[0m
Jan 25 00:10:09 runsvdir: ^[[33;22m[124440990][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:09 runsvdir: ^[[0m
Jan 25 00:10:09 runsvdir: ^[[33;22m[124441363][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:09 runsvdir: ^[[0m
Jan 25 00:10:09 runsvdir: ^[[33;22m[124441403][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:09 runsvdir: ^[[0m
Jan 25 00:10:09 runsvdir: ^[[33;22m[124441416][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:09 runsvdir: ^[[0m
Jan 25 00:10:09 runsvdir: ^[[33;22m[124441430][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:09 runsvdir: ^[[0m
Jan 25 00:10:09 runsvdir: ^[[33;22m[124441443][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:09 runsvdir: ^[[0m
Jan 25 00:10:09 runsvdir: ^[[33;22m[124442383][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:09 runsvdir: ^[[0m
Jan 25 00:10:09 runsvdir: ^[[33;22m[124443017][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:09 runsvdir: ^[[0m
Jan 25 00:10:09 runsvdir: ^[[33;22m[124443627][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:09 runsvdir: ^[[0m
Jan 25 00:10:09 runsvdir: ^[[33;22m[124443681][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:09 runsvdir: ^[[0m
Jan 25 00:10:09 runsvdir: ^[[33;22m[124443721][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:09 runsvdir: ^[[0m
Jan 25 00:10:09 runsvdir: ^[[33;22m[124443811][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:09 runsvdir: ^[[0m
Jan 25 00:10:09 runsvdir: ^[[33;22m[124443827][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:09 runsvdir: ^[[0m
Jan 25 00:10:09 runsvdir: ^[[33;22m[124443894][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:09 runsvdir: ^[[0m
Jan 25 00:10:09 runsvdir: ^[[33;22m[124444024][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:09 runsvdir: ^[[0m
Jan 25 00:10:09 runsvdir: ^[[33;22m[124444051][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:09 runsvdir: ^[[0m
Jan 25 00:10:09 runsvdir: ^[[33;22m[124444091][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:09 runsvdir: ^[[0m
Jan 25 00:10:09 runsvdir: ^[[33;22m[124445811][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:09 runsvdir: ^[[0m
Jan 25 00:10:09 runsvdir: ^[[33;22m[124446048][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:09 runsvdir: ^[[0m
Jan 25 00:10:09 runsvdir: ^[[33;22m[124446051][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:09 runsvdir: ^[[0m
Jan 25 00:10:09 runsvdir: ^[[33;22m[124447929][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:09 runsvdir: ^[[0m
Jan 25 00:10:09 runsvdir: ^[[33;22m[124448339][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:09 runsvdir: ^[[0m
Jan 25 00:10:09 runsvdir: ^[[33;22m[124448382][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:09 runsvdir: ^[[0m
Jan 25 00:10:09 runsvdir: ^[[33;22m[124450116][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:09 runsvdir: ^[[0m
Jan 25 00:10:09 runsvdir: ^[[33;22m[124450139][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:09 runsvdir: ^[[0m
Jan 25 00:10:09 runsvdir: ^[[33;22m[124451173][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:09 runsvdir: ^[[0m
Jan 25 00:10:09 runsvdir: ^[[33;22m[124451186][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:09 runsvdir: ^[[0m
Jan 25 00:10:13 runsvdir: ^[[33;22m[124451239][0300][0x7f823e520ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:13 runsvdir: [audio service]:try_again
Jan 25 00:10:13 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:13 runsvdir: [audio service]:try_again
Jan 25 00:10:13 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:13 runsvdir: [audio service]:try_again
Jan 25 00:10:13 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:13 runsvdir: [audio service]:try_again
Jan 25 00:10:16 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:16 runsvdir: [audio service]:try_again
Jan 25 00:10:16 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:16 runsvdir: [audio service]:try_again
Jan 25 00:10:16 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:16 runsvdir: [audio service]:try_again
Jan 25 00:10:16 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:16 runsvdir: [audio service]:try_again
Jan 25 00:10:16 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:16 runsvdir: [audio service]:try_again
Jan 25 00:10:16 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:16 runsvdir: [audio service]:try_again
Jan 25 00:10:22 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:22 runsvdir: [audio service]:try_again
Jan 25 00:10:22 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:22 runsvdir: [audio service]:try_again
Jan 25 00:10:22 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:22 runsvdir: [audio service]:try_again
Jan 25 00:10:23 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:23 runsvdir: [audio service]:try_again
Jan 25 00:10:23 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:23 runsvdir: [audio service]:try_again
Jan 25 00:10:25 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:25 runsvdir: [audio service]:try_again
Jan 25 00:10:26 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:26 runsvdir: [audio service]:try_again
Jan 25 00:10:27 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:27 runsvdir: [audio service]:try_again
Jan 25 00:10:27 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:27 runsvdir: [audio service]:try_again
Jan 25 00:10:29 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:29 runsvdir: [audio service]:try_again
Jan 25 00:10:29 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:29 runsvdir: [audio service]:try_again
Jan 25 00:10:29 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:29 runsvdir: [audio service]:try_again
Jan 25 00:10:32 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:32 runsvdir: [audio service]:try_again
Jan 25 00:10:33 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:33 runsvdir: [audio service]:try_again
Jan 25 00:10:33 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:33 runsvdir: [audio service]:try_again
Jan 25 00:10:33 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:33 runsvdir: [audio service]:try_again
Jan 25 00:10:33 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:33 runsvdir: [audio service]:try_again
Jan 25 00:10:34 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:34 runsvdir: [audio service]:try_again
Jan 25 00:10:35 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:35 runsvdir: [audio service]:try_again
Jan 25 00:10:35 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:35 runsvdir: [audio service]:try_again
Jan 25 00:10:35 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:35 runsvdir: [audio service]:try_again
Jan 25 00:10:36 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:36 runsvdir: [audio service]:try_again
Jan 25 00:10:36 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:36 runsvdir: [audio service]:try_again
Jan 25 00:10:36 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:36 runsvdir: [audio service]:try_again
Jan 25 00:10:39 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:39 runsvdir: [audio service]:try_again
Jan 25 00:10:39 runsvdir: 0][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:39 runsvdir: ^[[0m
Jan 25 00:10:39 runsvdir: ^[[33;22m[124451360][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:39 runsvdir: ^[[0m
Jan 25 00:10:39 runsvdir: ^[[33;22m[124451370][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:39 runsvdir: ^[[0m
Jan 25 00:10:39 runsvdir: ^[[33;22m[124451413][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:39 runsvdir: ^[[0m
Jan 25 00:10:39 runsvdir: ^[[33;22m[124451426][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:39 runsvdir: ^[[0m
Jan 25 00:10:39 runsvdir: ^[[33;22m[124451453][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:39 runsvdir: ^[[0m
Jan 25 00:10:39 runsvdir: ^[[33;22m[124451466][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:39 runsvdir: ^[[0m
Jan 25 00:10:39 runsvdir: ^[[33;22m[124451690][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:39 runsvdir: ^[[0m
Jan 25 00:10:39 runsvdir: ^[[31;22m[124451690][0300][0x7f823e5200][AIO_CORE][ERROR] i2s.c: ar_i2s_dma_isr : 413 [ahb-4] i2s int blk error: next:0x3cc1d039 h:0x200 l:0x1a209425.
Jan 25 00:10:39 runsvdir: ^[[0m
Jan 25 00:10:39 runsvdir: ^[[33;22m[124452720][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:39 runsvdir: ^[[0m
Jan 25 00:10:39 runsvdir: ^[[33;22m[124452763][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:39 runsvdir: ^[[0m
Jan 25 00:10:39 runsvdir: ^[[33;22m[124452787][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:39 runsvdir: ^[[0m
Jan 25 00:10:39 runsvdir: ^[[33;22m[124453027][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:39 runsvdir: ^[[0m
Jan 25 00:10:39 runsvdir: ^[[33;22m[124453727][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:39 runsvdir: ^[[0m
Jan 25 00:10:39 runsvdir: ^[[33;22m[124454218][0300][0x7f80738200][VO_CORE][WARN] display_api.c: dc_deinit : 2323 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:10:39 runsvdir: ^[[33;22m[124454218][0300][0x7f8081b200][VO_CORE][WARN] display_api.c: dc_deinit : 2323 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:10:39 runsvdir: ^[[35;22m[124454218][0300][0x7f82382200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:10:39 runsvdir: ^[[35;22m[124454218][0300][0x7f82382200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:10:39 runsvdir: ^[[35;22m[124454219][0300][0x7f8081b200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_close_camera : 961 camera_id=1^[[0m
Jan 25 00:10:39 runsvdir: open the driver sns:dp_rx obj:stSnsdp_rxObj lib:libsns_dp_rx.so 
Jan 25 00:10:39 runsvdir: ^[[31;22m[25 00:10:39.235448][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3559 !!!!!!the vif is force to dpcm duce to reduce bandwidth, if you want to use the dng, please set the pstPipeAttr->enEnableN
Jan 25 00:10:39 runsvdir: ^[[31;22m[25 00:10:39.235482][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3603 !!!!!!! Note: your app cfg the vif write compress on,so you must cfg the vif fre >=3*mipi fre !!!!!!^[[0m
Jan 25 00:10:39 runsvdir: ^[[31;22m[25 00:10:39.235642][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7846 not supported addr type^[[0m
Jan 25 00:10:39 runsvdir: ^[[31;22m[25 00:10:39.235655][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7856 not supported data type^[[0m
Jan 25 00:10:39 runsvdir: ^[[35;22m[124454221][0300][0x7f806f6200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_open_camera : 920 camera_id=1^[[0m
Jan 25 00:10:39 runsvdir: ^[[35;22m[124454221][0300][0x7f80738200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:10:39 runsvdir: ^[[35;22m[124454221][0300][0x7f80738200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:10:39 runsvdir: ^[[35;22m[124454221][0300][0x7f808c0200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:10:39 runsvdir: ^[[35;22m[124454221][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:10:39 runsvdir: ^[[35;22m[124454221][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:10:39 runsvdir: 36e7d000 372b7000 373c7000 0 0 0 374d1000 3790b000 37a1b000 0 0 0 
Jan 25 00:10:39 runsvdir: ^[[35;22m[124454221][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:10:39 runsvdir: aec index 198685398 line 1125 gain^[[0;31mDisplayService E: screen_get_duty[274] Failed to get duty. Error: -2
Jan 25 00:10:39 runsvdir: ^[[0;39m
Jan 25 00:10:39 runsvdir: ice I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:39 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:39 runsvdir: DisplayService I : Get frame stats: Successes 60.33, Failures 0.00
Jan 25 00:10:39 runsvdir: DisplayService I : dpvsync : 60.00
Jan 25 00:10:39 runsvdir: DisplayService I : get dp info 3840x1080@60
Jan 25 00:10:39 runsvdir: DisplayService I : check oled status:open, ret = 0
Jan 25 00:10:39 runsvdir: DisplayService I : set duty 0
Jan 25 00:10:39 runsvdir: DisplayService I : <timestamp open/close oled> -- [ 639.419 ]
Jan 25 00:10:39 runsvdir: DisplayService I : Screen successfully closed.
Jan 25 00:10:39 runsvdir: DisplayService I : get dp info 3840x1080@60
Jan 25 00:10:39 runsvdir: DisplayService I : get a cmd: stop dpisp
Jan 25 00:10:39 runsvdir: DisplayService I : dpisp get a new state:0
Jan 25 00:10:39 runsvdir: DisplayService I : <timestamp DPSIP stop start> -- [ 639.647 ]
Jan 25 00:10:39 runsvdir: DisplayService I : <timestamp force release vb done> -- [ 639.647 ]
Jan 25 00:10:39 runsvdir: DisplayService I : free vb count = 8, inused vb count = 0
Jan 25 00:10:39 runsvdir: DisplayService I : <timestamp DPSIP stop done> -- [ 639.657 ]
Jan 25 00:10:39 runsvdir: DisplayService D : destory pipi = 2, isp object = 0x7f91f88108
Jan 25 00:10:39 runsvdir: DisplayService I : <timestamp destory vi done> -- [ 639.657 ]
Jan 25 00:10:39 runsvdir: DisplayService I : current dpisp status update :0
Jan 25 00:10:39 runsvdir: DisplayOLED I : screen soft in change refresh reset successfully!
Jan 25 00:10:39 runsvdir: DisplayOLED I : OLED no set force lane!
Jan 25 00:10:39 runsvdir: DisplayOLED I : GF screen set inverse scan direction
Jan 25 00:10:39 runsvdir: DisplayService I : Configure OLED to 5lanesX2@90Hz Success
Jan 25 00:10:39 runsvdir: DisplayService I : get cmd CMD_VI_RESUME_BY_VO
Jan 25 00:10:39 runsvdir: DisplayService I : dpisp get a new state:1
Jan 25 00:10:39 runsvdir: DisplayService D : get dprx so
Jan 25 00:10:39 runsvdir: DisplayService D : init pipi = 2, isp object = 0x7f91f88108
Jan 25 00:10:39 runsvdir: DisplayService I : enPixelFormat:0x17
Jan 25 00:10:39 runsvdir: DisplayService I : get dpvif_fre_hz = 333000000
Jan 25 00:10:39 runsvdir: DisplayService I : >>>>>> stComboAttr >>>>>>>
Jan 25 00:10:39 runsvdir: DisplayService I : devno: 0
Jan 25 00:10:39 runsvdir: DisplayService I : input_mode: 0x0b
Jan 25 00:10:39 runsvdir: DisplayService I : data_rate : 450
Jan 25 00:10:39 runsvdir: DisplayService I : mipi-input_data_type: 0x02
Jan 25 00:10:39 runsvdir: DisplayService I : stSnsSize.u32Width = 3840
Jan 25 00:10:39 runsvdir: DisplayService I : stSnsSize.u32Height = 1080
Jan 25 00:10:39 runsvdir: DisplayService I : stTiming.hblank = 560
Jan 25 00:10:39 runsvdir: DisplayService I : stTiming.vblank = 45
Jan 25 00:10:39 runsvdir: DisplayService I : stChnAttr.stSize.u32Width = 3840
Jan 25 00:10:39 runsvdir: DisplayService I : stChnAttr.stSize.u32Height = 1080
Jan 25 00:10:39 runsvdir: DisplayService I : stChnAttr.u32BufCount = 8
Jan 25 00:10:39 runsvdir: DisplayService I : stPubAttr.f32FrameRate = 60.000000
Jan 25 00:10:39 runsvdir: DisplayService I : <<<<<< stComboAttr <<<<<<<
Jan 25 00:10:39 runsvdir: read vb config data index:4 success.
Jan 25 00:10:39 runsvdir: DisplayService I : pipe:2 channel:0 get vb pool id <4>!
Jan 25 00:10:39 runsvdir: read vb config data index:4 success.
Jan 25 00:10:39 runsvdir: get vb pool id <4>!
Jan 25 00:10:39 runsvdir: DisplayService I : pipe:2 chn:0 bind vb pool:4 success!
Jan 25 00:10:39 runsvdir: DisplayService I : <timestamp AR_MPI_VI_GetHightPricisionPipeFPS start> -- [ 639.683 ]
Jan 25 00:10:39 runsvdir: DisplayService I : get high pricision fps = 60.002216
Jan 25 00:10:39 runsvdir: DisplayService I : <timestamp AR_MPI_VI_GetHightPricisionPipeFPS end> -- [ 639.844 ]
Jan 25 00:10:39 runsvdir: DisplayService I : isp attr: type : bt709, color range : full range
Jan 25 00:10:39 runsvdir: DisplayService I : <timestamp init vi done> -- [ 639.851 ]
Jan 25 00:10:39 runsvdir: DisplayService I : current dpisp status update :1
Jan 25 00:10:39 runsvdir: DisplayService I : get a cmd: start dpisp
Jan 25 00:10:39 runsvdir: DisplayService I : check oled status:close, ret = 0
Jan 25 00:10:39 runsvdir: DisplayService I : dpisp get a new state:2
Jan 25 00:10:39 runsvdir: DisplayService I : only enable dpisp
Jan 25 00:10:39 runsvdir: DisplayService I : <timestamp init isp > -- [ 639.883 ]
Jan 25 00:10:39 runsvdir: DisplayService I : enable vi channel output
Jan 25 00:10:39 runsvdir: DisplayService I : set duty 0
Jan 25 00:10:39 runsvdir: DisplayService I : Setting new LUT for mode: dp_scaler_lut_direct_mode_1
Jan 25 00:10:39 runsvdir: DisplaySer 1.000000 
Jan 25 00:10:39 runsvdir: ^[[35;22m[124454222][0300][0x7f808c0200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:10:39 runsvdir: ^[[35;22m[124454222][0300][0x7f808c0200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:10:39 runsvdir: 374d7000 37911000 37a21000 0 0 0 37b2b000 37f65000 38075000 0 0 0 
Jan 25 00:10:39 runsvdir: ^[[35;22m[124454224][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:10:39 runsvdir: ^[[35;22m[124454226][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:10:39 runsvdir: ^[[35;22m[124454238][0300][0x7f813ae200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:10:39 runsvdir: ^[[35;22m[124454238][0300][0x7f813ae200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:10:39 runsvdir: ^[[31;22m[124454241][0498][0x7ef28781a0][VPSS_MPP][ERROR] mpi_vpss.c: ar_hal_vpss_create : 870 bind_register_sender failed 0x80028003^[[0m
Jan 25 00:10:39 runsvdir: ^[[33;22m[124454241][0300][0x7f813ae200][VO_CORE][WARN] display_api.c: dc_init : 2258 use dc_reg_irq_by_cmd!^[[0m
Jan 25 00:10:39 runsvdir: ^[[31;22m[124454241][0300][0x7f813ae200][VO_CORE][ERROR] display_top.c: ar_display_dev_enable : 3521 not support set irq_type:^[[33;22m[124454241][0300][0x7f812a9200][AXIMGR_CORE][WARN] ar_aximgr_lb.c: ar_aximgr_lb_enable : 285
Jan 25 00:10:39 runsvdir: ^[[35;22m[124454242][0300][0x7f808e1200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:10:39 runsvdir: ^[[35;22m[124454242][0300][0x7f808e1200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:10:39 runsvdir: ^[[35;22m[124454242][0300][0x7f808c0200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:10:39 runsvdir: ^[[35;22m[124454242][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:10:39 runsvdir: ^[[35;22m[124454242][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:10:39 runsvdir: 36e7d000 372b7000 373c7000 0 0 0 374d1000 3790b000 37a1b000 0 0 0 
Jan 25 00:10:39 runsvdir: ^[[35;22m[124454242][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:10:39 runsvdir: aec index 198685398 line 1125 gain 1.000000 
Jan 25 00:10:39 runsvdir: ^[[35;22m[124454242][0300][0x7f808c0200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:10:39 runsvdir: ^[[35;22m[124454242][0300][0x7f808c0200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:10:39 runsvdir: 374d7000 37911000 37a21000 0 0 0 37b2b000 37f65000 38075000 0 0 0 
Jan 25 00:10:39 runsvdir: ^[[35;22m[124454244][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:10:39 runsvdir: ^[[35;22m[124454246][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:10:39 runsvdir: ^[[33;22m[124454249][0300][0x7f808c0200][VO_CORE][WARN] display_api.c: dc_deinit : 2323 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:10:39 runsvdir: ^[[33;22m[124454250][0300][0x7f80759200][VO_CORE][WARN] display_api.c: dc_deinit : 2323 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:10:39 runsvdir: ^[[35;22m[124454250][0300][0x7f81f5f200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:10:39 runsvdir: ^[[35;22m[124454251][0300][0x7f81f5f200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:10:39 runsvdir: ^[[35;22m[124454252][0300][0x7f80759200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_close_camera : 961 camera_id=1^[[0m
Jan 25 00:10:39 runsvdir: open the driver sns:dp_rx obj:stSnsdp_rxObj lib:libsns_dp_rx.so 
Jan 25 00:10:39 runsvdir: ^[[31;22m[25 00:10:39.552889][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3559 !!!!!!the vif is force to dpcm duce to reduce bandwidth, if you want to use the dng, please set the pstPipeAttr->enEnableN
Jan 25 00:10:39 runsvdir: ^[[31;22m[25 00:10:39.552931][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3603 !!!!!!! Note: your app cfg the vif write compress on,so you must cfg the vif fre >=3*mipi fre !!!!!!^[[0m
Jan 25 00:10:39 runsvdir: ^[[31;22m[25 00:10:39.553113][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7846 not supported addr type^[[0m
Jan 25 00:10:40 runsvdir: ^[[31;22m[25 00:10:39.553152][0313][0x7f896db1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPuALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:40 runsvdir: [audio service]:try_again
Jan 25 00:10:40 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:40 runsvdir: [audio service]:try_again
Jan 25 00:10:40 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:40 runsvdir: [audio service]:try_again
Jan 25 00:10:40 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:40 runsvdir: [audio service]:try_again
Jan 25 00:10:40 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:40 runsvdir: [audio service]:try_again
Jan 25 00:10:40 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:40 runsvdir: [audio service]:try_again
Jan 25 00:10:43 runsvdir: bAttr : 7856 not supported data type^[[0m
Jan 25 00:10:43 runsvdir: ^[[35;22m[124454252][0300][0x7f812a9200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_open_camera : 920 camera_id=1^[[0m
Jan 25 00:10:43 runsvdir: ^[[35;22m[124454253][0300][0x7f81f5f200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:10:43 runsvdir: ^[[35;22m[124454253][0300][0x7f81f5f200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:10:43 runsvdir: ^[[35;22m[124454253][0300][0x7f812a9200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:10:43 runsvdir: ^[[35;22m[124454253][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:10:43 runsvdir: 36e7d000 372b7000 373c7000 0 0 0 374d1000 3790b000 37a1b000 0 0 0 
Jan 25 00:10:43 runsvdir: ^[[35;22m[124454253][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:10:43 runsvdir: ^[[35;22m[124454254][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:10:43 runsvdir: aec index 198685398 line 1125 gain 1.000000 
Jan 25 00:10:43 runsvdir: ^[[35;22m[124454254][0300][0x7f812a9200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:10:43 runsvdir: ^[[35;22m[124454254][0300][0x7f812a9200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:10:43 runsvdir: 374d7000 37911000 37a21000 0 0 0 37b2b000 37f65000 38075000 0 0 0 
Jan 25 00:10:43 runsvdir: ^[[35;22m[124454256][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:10:43 runsvdir: ^[[35;22m[124454258][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:10:43 runsvdir: ^[[35;22m[124454270][0300][0x7f80717200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:10:43 runsvdir: ^[[35;22m[124454270][0300][0x7f80717200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:10:43 runsvdir: ^[[31;22m[124454272][0498][0x7ef28781a0][VPSS_MPP][ERROR] mpi_vpss.c: ar_hal_vpss_create : 870 bind_register_sender failed 0x80028003^[[0m
Jan 25 00:10:43 runsvdir: ^[[33;22m[124454273][0300][0x7f81f5f200][VO_CORE][WARN] display_api.c: dc_init : 2258 use dc_reg_irq_by_cmd!^[[0m
Jan 25 00:10:43 runsvdir: ^[[31;22m[124454273][0300][0x7f81f5f200][VO_CORE][ERROR] display_top.c: ar_display_dev_enable : 3521 not support set irq_type:^[[33;22m[124454273][0300][0x7f813ae200][AXIMGR_CORE][WARN] ar_aximgr_lb.c: ar_aximgr_lb_enable : 285
Jan 25 00:10:43 runsvdir: ^[[33;22m[124454273][0300][0x7f812a9200][AXIMGR_CORE][WARN] ar_aximgr_lb.c: ar_aximgr_lb_enable : 285 [LB] 1920 1024 1024^[[0m
Jan 25 00:10:43 runsvdir: ^[[35;22m[124454273][0300][0x7f80738200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:10:43 runsvdir: ^[[35;22m[124454274][0300][0x7f80738200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:10:43 runsvdir: ^[[35;22m[124454274][0300][0x7f8081b200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:10:43 runsvdir: ^[[35;22m[124454274][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:10:43 runsvdir: ^[[35;22m[124454274][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:10:43 runsvdir: ^[[35;22m[124454274][0300][0x7f81246200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:10:43 runsvdir: 36e7d000 372b7000 373c7000 0 0 0 374d1000 3790b000 37a1b000 0 0 0 
Jan 25 00:10:43 runsvdir: aec index 198685398 line 1125 gain 1.000000 
Jan 25 00:10:43 runsvdir: ^[[35;22m[124454274][0300][0x7f8081b200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:10:43 runsvdir: ^[[35;22m[124454274][0300][0x7f8081b200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:10:43 runsvdir: 374d7000 37911000 37a21000 0 0 0 37b2b000 37f65000 38075000 0 0 0 
Jan 25 00:10:43 runsvdir: ^[[35;22m[124454276][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:10:43 runsvdir: ^[[35;22m[124454278][0300][0x7f81616200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:10:43 runsvdir: ^[[33;22m[124454427][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:43 runsvdir: ^[[0m
Jan 25 00:10:43 runsvdir: ^[[33;22m[124454467][0300][0x7f823e5200][AIO_CORE][WARN] i2s.c: ar_i2s_dma_isr : 473 [ahb-4] i2s in int lost 2 read num 2
Jan 25 00:10:43 runsvdir: ^[[0m
Jan 25 00:10:43 runsvdir: ^[[33;22m[124454601][0300]ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:43 runsvdir: [audio service]:try_again
Jan 25 00:10:45 runsvdir: ALSA lib pcm.c:8740:(snd_pcm_recover) overrun occurred
Jan 25 00:10:45 runsvdir: [audio service]:try_again
Jan 25 00:10:46 runsvdir: vice I : <timestamp init isp done> -- [ 639.891 ]
Jan 25 00:10:46 runsvdir: DisplayService I : current dpisp status update :2
Jan 25 00:10:46 runsvdir: DisplayService I : <timestamp open/close oled> -- [ 639.904 ]
Jan 25 00:10:46 runsvdir: DisplayService I : Screen successfully opened.
Jan 25 00:10:46 runsvdir: DisplayService I : set duty 30
Jan 25 00:10:46 runsvdir: DisplayService I : check oled status:open, ret = 0
Jan 25 00:10:46 runsvdir: DisplayService I : set duty 30
Jan 25 00:10:46 runsvdir: DisplayService I : get dp info 3840x1080@60
Jan 25 00:10:46 runsvdir: DisplayService W : can't find node 0x7f8c001dd0, ignore this release
Jan 25 00:10:46 runsvdir: DisplayService I : get dp info 3840x1080@60
Jan 25 00:10:46 runsvdir: DisplayService I : check oled status:open, ret = 0
Jan 25 00:10:46 runsvdir: DisplayService I : set duty 0
Jan 25 00:10:46 runsvdir: DisplayService I : <timestamp open/close oled> -- [ 639.942 ]
Jan 25 00:10:46 runsvdir: DisplayService I : Screen successfully closed.
Jan 25 00:10:46 runsvdir: DisplayService I : get dp info 3840x1080@60
Jan 25 00:10:46 runsvdir: DisplayService I : get a cmd: stop dpisp
Jan 25 00:10:46 runsvdir: DisplayService I : dpisp get a new state:0
Jan 25 00:10:46 runsvdir: DisplayService I : <timestamp DPSIP stop start> -- [ 639.965 ]
Jan 25 00:10:46 runsvdir: DisplayService I : <timestamp force release vb done> -- [ 639.965 ]
Jan 25 00:10:46 runsvdir: DisplayService I : free vb count = 8, inused vb count = 0
Jan 25 00:10:46 runsvdir: DisplayService I : <timestamp DPSIP stop done> -- [ 639.985 ]
Jan 25 00:10:46 runsvdir: DisplayService D : destory pipi = 2, isp object = 0x7f91f88108
Jan 25 00:10:46 runsvdir: DisplayService I : <timestamp destory vi done> -- [ 639.985 ]
Jan 25 00:10:46 runsvdir: DisplayService I : current dpisp status update :0
Jan 25 00:10:46 runsvdir: DisplayOLED I : screen soft in change refresh reset successfully!
Jan 25 00:10:46 runsvdir: DisplayOLED I : OLED no set force lane!
Jan 25 00:10:46 runsvdir: DisplayOLED I : GF screen set inverse scan direction
Jan 25 00:10:46 runsvdir: DisplayService I : Configure OLED to 5lanesX2@90Hz Success
Jan 25 00:10:46 runsvdir: DisplayService I : get cmd CMD_VI_RESUME_BY_VO
Jan 25 00:10:46 runsvdir: DisplayService I : dpisp get a new state:1
Jan 25 00:10:46 runsvdir: DisplayService D : get dprx so
Jan 25 00:10:46 runsvdir: DisplayService D : init pipi = 2, isp object = 0x7f91f88108
Jan 25 00:10:46 runsvdir: DisplayService I : enPixelFormat:0x17
Jan 25 00:10:46 runsvdir: DisplayService I : get dpvif_fre_hz = 333000000
Jan 25 00:10:46 runsvdir: DisplayService I : >>>>>> stComboAttr >>>>>>>
Jan 25 00:10:46 runsvdir: DisplayService I : devno: 0
Jan 25 00:10:46 runsvdir: DisplayService I : input_mode: 0x0b
Jan 25 00:10:46 runsvdir: DisplayService I : data_rate : 450
Jan 25 00:10:46 runsvdir: DisplayService I : mipi-input_data_type: 0x02
Jan 25 00:10:46 runsvdir: DisplayService I : stSnsSize.u32Width = 3840
Jan 25 00:10:46 runsvdir: DisplayService I : stSnsSize.u32Height = 1080
Jan 25 00:10:46 runsvdir: DisplayService I : stTiming.hblank = 560
Jan 25 00:10:46 runsvdir: DisplayService I : stTiming.vblank = 45
Jan 25 00:10:46 runsvdir: DisplayService I : stChnAttr.stSize.u32Width = 3840
Jan 25 00:10:46 runsvdir: DisplayService I : stChnAttr.stSize.u32Height = 1080
Jan 25 00:10:46 runsvdir: DisplayService I : stChnAttr.u32BufCount = 8
Jan 25 00:10:46 runsvdir: DisplayService I : stPubAttr.f32FrameRate = 60.000000
Jan 25 00:10:46 runsvdir: DisplayService I : <<<<<< stComboAttr <<<<<<<
Jan 25 00:10:46 runsvdir: read vb config data index:4 success.
Jan 25 00:10:46 runsvdir: DisplayService I : pipe:2 channel:0 get vb pool id <4>!
Jan 25 00:10:46 runsvdir: read vb config data index:4 success.
Jan 25 00:10:46 runsvdir: get vb pool id <4>!
Jan 25 00:10:46 runsvdir: DisplayService I : pipe:2 chn:0 bind vb pool:4 success!
Jan 25 00:10:46 runsvdir: DisplayService I : <timestamp AR_MPI_VI_GetHightPricisionPipeFPS start> -- [ 640.7 ]
Jan 25 00:10:46 runsvdir: DisplayService I : get high pricision fps = 60.002216
Jan 25 00:10:46 runsvdir: DisplayService I : <timestamp AR_MPI_VI_GetHightPricisionPipeFPS end> -- [ 640.168 ]
Jan 25 00:10:46 runsvdir: DisplayService I : isp attr: type : bt709, color range : full range
Jan 25 00:10:46 runsvdir: DisplayService I : <timestamp init vi done> -- [ 640.174 ]
Jan 25 00:10:46 runsvdir: DisplayService I : current dpisp status update :1
Jan 25 00:10:46 runsvdir: DisplayService I : get a cmd: start dpisp
Jan 25 00:10:46 runsvdir: DisplayService I : dpisp get a new state:2
Jan 25 00:10:46 runsvdir: DisplayService I : only enable dpisp
Jan 25 00:10:46 runsvdir: DisplayService I : <timestamp init isp > -- [ 640.202 ]
Jan 25 00:10:46 runsvdir: DisplayService I : enable vi channel output
Jan 25 00:10:46 runsvdir: DisplayService I : check oled status:close, ret = 0
Jan 25 00:10:46 runsvdir: DisplayService I : set duty 0
Jan 25 00:10:46 runsvdir: DisplayService I : Setting new LUT for mode: dp_scaler_lut_direct_mode_1
Jan 25 00:10:46 runsvdir: DisplayService I : <timestamp init isp done> -- [ 640.211 ]
Jan 25 00:10:46 runsvdir: DisplayService I : current dpisp status update :2
Jan 25 00:10:46 runsvdir: DisplayService I : <timestamp open/close oled> -- [ 640.226 ]
Jan 25 00:10:46 runsvdir: DisplayService I : Screen successfully opened.
Jan 25 00:10:46 runsvdir: DisplayService I : set duty 30
Jan 25 00:10:46 runsvdir: DisplayService I : check oled status:open, ret = 0
Jan 25 00:10:46 runsvdir: DisplayService I : set duty 30
Jan 25 00:10:46 runsvdir: DisplayService I : get dp info 3840x1080@60
Jan 25 00:10:46 runsvdir: DisplayService W : can't find node 0x7f8c0020a0, ignore this release
Jan 25 00:10:46 runsvdir: DisplayService W : can't find node 0x7f8c002640, ignore this release
Jan 25 00:10:46 runsvdir: DisplayService W : can't find node 0x7f8c002910, ignore this release
Jan 25 00:10:46 runsvdir: DisplayService I : Get frame stats: Successes 44.00, Failures 0.00
Jan 25 00:10:46 runsvdir: DisplayService I : dpvsync : 57.67
Jan 25 00:10:46 runsvdir: DisplayService I : Get frame stats: Successes 60.00, Failures 0.00
Jan 25 00:10:46 runsvdir: DisplayService I : dpvsync : 60.00
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   